<Window x:Class="Sellsys.WpfClient.Views.Dialogs.AssignSalesDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="分配销售"
        Height="550" Width="500"
        MinHeight="500" MinWidth="450"
        MaxHeight="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        ShowInTaskbar="False">
    
    <Window.Resources>
        <!-- 弹窗样式 -->
        <Style x:Key="DialogTitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="15,0"/>
        </Style>
        
        <Style x:Key="DialogLabelStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#303133"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>
        
        <Style x:Key="SectionTitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#303133"/>
            <Setter Property="Margin" Value="0,20,0,20"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/> <!-- Title Bar -->
            <RowDefinition Height="*"/>  <!-- Content -->
            <RowDefinition Height="80" MinHeight="80"/> <!-- Buttons -->
        </Grid.RowDefinitions>

        <!-- Title Bar -->
        <Border Grid.Row="0" Background="{StaticResource PopupTitleBrush}">
            <TextBlock Text="分配销售" Style="{StaticResource DialogTitleStyle}"/>
        </Border>

        <!-- Content Area -->
        <ScrollViewer Grid.Row="1"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Margin="30,20,30,20">
            <Grid>
                <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 标题 -->
            <TextBlock Grid.Row="0" 
                       Text="分配给销售：" 
                       Style="{StaticResource SectionTitleStyle}"/>

            <!-- 部门名称 -->
            <Grid Grid.Row="1" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <TextBlock Grid.Column="0" Text="部门名称：" Style="{StaticResource DialogLabelStyle}"/>
                <ComboBox Grid.Column="1"
                          ItemsSource="{Binding Departments}"
                          SelectedItem="{Binding SelectedDepartment}"
                          DisplayMemberPath="Name"
                          Style="{StaticResource StandardComboBoxStyle}"/>
            </Grid>

            <!-- 组别名称 -->
            <Grid Grid.Row="2" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <TextBlock Grid.Column="0" Text="组别名称：" Style="{StaticResource DialogLabelStyle}"/>
                <ComboBox Grid.Column="1" 
                          ItemsSource="{Binding Groups}"
                          SelectedItem="{Binding SelectedGroup}"
                          DisplayMemberPath="Name"
                          Style="{StaticResource StandardComboBoxStyle}"/>
            </Grid>

            <!-- 销售姓名 -->
            <Grid Grid.Row="3" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <TextBlock Grid.Column="0" Text="销售姓名：" Style="{StaticResource DialogLabelStyle}"/>
                <ComboBox Grid.Column="1" 
                          ItemsSource="{Binding SalesPersons}"
                          SelectedItem="{Binding SelectedSalesPerson}"
                          DisplayMemberPath="Name"
                          Style="{StaticResource StandardComboBoxStyle}"/>
            </Grid>
            </Grid>
        </ScrollViewer>

        <!-- Button Area -->
        <Border Grid.Row="2" Background="#F8F9FA" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="20,22,20,22" VerticalAlignment="Center">
                <Button Content="取消"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource DeleteButtonStyle}"
                        Width="80"
                        Height="35"
                        Margin="0,0,10,0"
                        VerticalAlignment="Center"/>
                <Button Command="{Binding AssignCommand}"
                        Width="80"
                        Height="35"
                        Margin="0"
                        VerticalAlignment="Center">
                    <Button.Style>
                        <Style TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
                            <Setter Property="Content" Value="分配"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Content" Value="分配中..."/>
                                    <Setter Property="IsEnabled" Value="False"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
