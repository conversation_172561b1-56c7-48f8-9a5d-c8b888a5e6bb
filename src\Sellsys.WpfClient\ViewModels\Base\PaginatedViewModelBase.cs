using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Sellsys.WpfClient.Commands;
using Sellsys.WpfClient.Models.Pagination;

namespace Sellsys.WpfClient.ViewModels.Base
{
    /// <summary>
    /// 分页ViewModel基类
    /// </summary>
    /// <typeparam name="T">数据项类型</typeparam>
    public abstract class PaginatedViewModelBase<T> : ViewModelBase
    {
        #region Private Fields
        private int _currentPage = 1;
        private int _totalPages = 1;
        private int _totalCount = 0;
        private int _pageSize = 13;
        private string _pageInfo = string.Empty;
        private bool _isLoading = false;
        private ObservableCollection<int> _pageNumbers = new ObservableCollection<int>();
        private int _jumpToPage = 1;
        #endregion

        #region Public Properties
        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage
        {
            get => _currentPage;
            set => SetProperty(ref _currentPage, value);
        }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages
        {
            get => _totalPages;
            set
            {
                if (SetProperty(ref _totalPages, value))
                {
                    UpdatePageNumbers();
                    UpdatePageInfo();
                }
            }
        }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount
        {
            get => _totalCount;
            set
            {
                if (SetProperty(ref _totalCount, value))
                {
                    UpdatePageInfo();
                }
            }
        }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize
        {
            get => _pageSize;
            set => SetProperty(ref _pageSize, value);
        }

        /// <summary>
        /// 页面信息显示文本
        /// </summary>
        public string PageInfo
        {
            get => _pageInfo;
            set => SetProperty(ref _pageInfo, value);
        }

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// 页码列表（用于显示页码按钮）
        /// </summary>
        public ObservableCollection<int> PageNumbers
        {
            get => _pageNumbers;
            set => SetProperty(ref _pageNumbers, value);
        }

        /// <summary>
        /// 跳转到指定页码
        /// </summary>
        public int JumpToPage
        {
            get => _jumpToPage;
            set => SetProperty(ref _jumpToPage, value);
        }

        /// <summary>
        /// 是否有上一页
        /// </summary>
        public bool HasPreviousPage => CurrentPage > 1;

        /// <summary>
        /// 是否有下一页
        /// </summary>
        public bool HasNextPage => CurrentPage < TotalPages;
        #endregion

        #region Commands
        public ICommand PreviousPageCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand GoToPageCommand { get; }
        public ICommand JumpToPageCommand { get; }
        public ICommand RefreshCommand { get; }
        #endregion

        #region Constructor
        protected PaginatedViewModelBase()
        {
            PreviousPageCommand = new AsyncRelayCommand(async p => await PreviousPageAsync(), p => HasPreviousPage);
            NextPageCommand = new AsyncRelayCommand(async p => await NextPageAsync(), p => HasNextPage);
            GoToPageCommand = new AsyncRelayCommand(async p => await GoToPageAsync(p), p => p is int);
            JumpToPageCommand = new AsyncRelayCommand(async p => await JumpToPageAsync(), p => CanJumpToPage());
            RefreshCommand = new AsyncRelayCommand(async p => await RefreshDataAsync());

            UpdatePageInfo();
        }
        #endregion

        #region Abstract Methods
        /// <summary>
        /// 加载分页数据的抽象方法，子类必须实现
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页结果</returns>
        protected abstract Task<PagedResult<T>> LoadPagedDataAsync(int pageNumber, int pageSize);
        #endregion

        #region Public Methods
        /// <summary>
        /// 加载数据
        /// </summary>
        public override async Task LoadDataAsync()
        {
            await LoadPageAsync(CurrentPage);
        }

        /// <summary>
        /// 刷新当前页数据
        /// </summary>
        public virtual async Task RefreshDataAsync()
        {
            await LoadPageAsync(CurrentPage);
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// 加载指定页的数据
        /// </summary>
        private async Task LoadPageAsync(int pageNumber)
        {
            if (IsLoading) return;

            try
            {
                IsLoading = true;
                var result = await LoadPagedDataAsync(pageNumber, PageSize);

                CurrentPage = result.PageNumber;
                TotalPages = result.TotalPages;
                TotalCount = result.TotalCount;

                // 更新分页UI信息
                UpdatePageNumbers();
                UpdatePageInfo();

                // 通知子类更新数据
                OnDataLoaded(result.Items);
            }
            catch (Exception ex)
            {
                // 处理错误
                OnLoadError(ex);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 上一页
        /// </summary>
        private async Task PreviousPageAsync()
        {
            if (HasPreviousPage)
            {
                await LoadPageAsync(CurrentPage - 1);
            }
        }

        /// <summary>
        /// 下一页
        /// </summary>
        private async Task NextPageAsync()
        {
            if (HasNextPage)
            {
                await LoadPageAsync(CurrentPage + 1);
            }
        }

        /// <summary>
        /// 跳转到指定页
        /// </summary>
        private async Task GoToPageAsync(object? parameter)
        {
            if (parameter is int pageNumber && pageNumber >= 1 && pageNumber <= TotalPages)
            {
                await LoadPageAsync(pageNumber);
            }
        }

        /// <summary>
        /// 跳转到输入的页码
        /// </summary>
        private async Task JumpToPageAsync()
        {
            if (JumpToPage >= 1 && JumpToPage <= TotalPages)
            {
                await LoadPageAsync(JumpToPage);
            }
        }

        /// <summary>
        /// 是否可以跳转到指定页
        /// </summary>
        private bool CanJumpToPage()
        {
            return JumpToPage >= 1 && JumpToPage <= TotalPages && !IsLoading;
        }

        /// <summary>
        /// 更新页码列表
        /// </summary>
        protected void UpdatePageNumbers()
        {
            PageNumbers.Clear();
            
            if (TotalPages <= 7)
            {
                // 如果总页数不超过7页，显示所有页码
                for (int i = 1; i <= TotalPages; i++)
                {
                    PageNumbers.Add(i);
                }
            }
            else
            {
                // 显示当前页附近的页码
                int start = Math.Max(1, CurrentPage - 3);
                int end = Math.Min(TotalPages, CurrentPage + 3);
                
                for (int i = start; i <= end; i++)
                {
                    PageNumbers.Add(i);
                }
            }
        }

        /// <summary>
        /// 更新页面信息
        /// </summary>
        protected void UpdatePageInfo()
        {
            PageInfo = $"第 {CurrentPage} 页，共 {TotalPages} 页，总计 {TotalCount} 条记录";
        }
        #endregion

        #region Virtual Methods
        /// <summary>
        /// 数据加载完成时调用，子类可重写
        /// </summary>
        protected virtual void OnDataLoaded(List<T> data)
        {
            // 默认实现为空，子类可重写
        }

        /// <summary>
        /// 加载错误时调用，子类可重写
        /// </summary>
        protected virtual void OnLoadError(Exception ex)
        {
            // 默认实现：输出调试信息
            System.Diagnostics.Debug.WriteLine($"分页数据加载失败: {ex.Message}");
        }
        #endregion
    }
}
