<Window x:Class="Sellsys.WpfClient.Views.Dialogs.AddOrderDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="添加订单"
        Height="750"
        Width="1100"
        MinHeight="600" MinWidth="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#409EFF" Padding="15">
            <StackPanel>
                <TextBlock Text="添加订单" 
                           Foreground="White" 
                           FontSize="18" 
                           FontWeight="Bold"/>
                <TextBlock Text="{Binding CustomerName, StringFormat='客户单位：{0}'}" 
                           Foreground="White" 
                           FontSize="14" 
                           Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Column -->
                <StackPanel Grid.Column="0">
                    <!-- 订单编号 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="订单编号：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <TextBox Grid.Column="1"
                                 Text="{Binding OrderNumber, Mode=OneWay}"
                                 Style="{StaticResource StandardTextBoxStyle}"
                                 IsReadOnly="True"
                                 Background="#F5F5F5"
                                 Height="32"/>
                    </Grid>

                    <!-- 客户名称 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="客户名称：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <TextBox Grid.Column="1"
                                 Text="{Binding CustomerName, Mode=OneWay}"
                                 Style="{StaticResource StandardTextBoxStyle}"
                                 IsReadOnly="True"
                                 Background="#F5F5F5"
                                 Height="32"/>
                    </Grid>

                    <!-- 产品名称 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="产品名称：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <TextBox Grid.Column="1"
                                 Text="{Binding SelectedProductName, Mode=OneWay}"
                                 Style="{StaticResource StandardTextBoxStyle}"
                                 IsReadOnly="True"
                                 Background="#F5F5F5"
                                 Height="32"
                                 Margin="0,0,10,0"/>
                        <Button Grid.Column="2"
                                Content="选择"
                                Command="{Binding SelectProductCommand}"
                                Style="{StaticResource PrimaryButtonStyle}"
                                Width="60"
                                Height="32"/>
                    </Grid>

                    <!-- 型号规格 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="型号规格：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <TextBox Grid.Column="1"
                                 Text="{Binding ProductSpecification, Mode=OneWay}"
                                 Style="{StaticResource StandardTextBoxStyle}"
                                 IsReadOnly="True"
                                 Background="#F5F5F5"
                                 Height="32"/>
                    </Grid>

                    <!-- 计量单位 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="计量单位：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <TextBox Grid.Column="1"
                                 Text="{Binding ProductUnit, Mode=OneWay}"
                                 Style="{StaticResource StandardTextBoxStyle}"
                                 IsReadOnly="True"
                                 Background="#F5F5F5"
                                 Height="32"/>
                    </Grid>

                    <!-- 生效日期 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="生效日期：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <DatePicker Grid.Column="1"
                                    SelectedDate="{Binding EffectiveDate}"
                                    Style="{StaticResource StandardDatePickerStyle}"
                                    Height="32"/>
                    </Grid>

                    <!-- 到期日期 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="到期日期：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <DatePicker Grid.Column="1"
                                    SelectedDate="{Binding ExpiryDate}"
                                    Style="{StaticResource StandardDatePickerStyle}"
                                    Height="32"/>
                    </Grid>
                </StackPanel>

                <!-- Right Column -->
                <StackPanel Grid.Column="2">
                    <!-- 产品定价 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="产品定价：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <TextBox Grid.Column="1"
                                 Text="{Binding ProductListPrice, StringFormat=F2, Mode=OneWay}"
                                 Style="{StaticResource StandardTextBoxStyle}"
                                 IsReadOnly="True"
                                 Background="#F5F5F5"
                                 Height="32"/>
                    </Grid>

                    <!-- 实际售价 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="实际售价：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <TextBox Grid.Column="1"
                                 Text="{Binding ActualPrice}"
                                 Style="{StaticResource StandardTextBoxStyle}"
                                 Height="32"/>
                    </Grid>

                    <!-- 销售数量 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="销售数量：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <TextBox Grid.Column="1"
                                 Text="{Binding Quantity}"
                                 Style="{StaticResource StandardTextBoxStyle}"
                                 Height="32"/>
                    </Grid>

                    <!-- 订单金额 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="订单金额：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <TextBox Grid.Column="1"
                                 Text="{Binding TotalAmount, StringFormat=F2, Mode=OneWay}"
                                 Style="{StaticResource StandardTextBoxStyle}"
                                 IsReadOnly="True"
                                 Background="#F5F5F5"
                                 Height="32"/>
                    </Grid>

                    <!-- 销售姓名 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="销售姓名：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <ComboBox Grid.Column="1"
                                  ItemsSource="{Binding SalesPersons}"
                                  SelectedItem="{Binding SelectedSalesPerson}"
                                  DisplayMemberPath="Name"
                                  Height="32"
                                  Style="{StaticResource StandardComboBoxStyle}"/>
                    </Grid>

                    <!-- 签单时间 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="签单时间：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <TextBox Grid.Column="1"
                                 Text="{Binding SignedTime, StringFormat='yyyy-MM-dd HH:mm:ss', Mode=OneWay}"
                                 Style="{StaticResource StandardTextBoxStyle}"
                                 IsReadOnly="True"
                                 Background="#F5F5F5"
                                 Height="32"/>
                    </Grid>

                    <!-- 订单状态 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="订单状态：" VerticalAlignment="Top" Style="{StaticResource DialogLabelStyle}" Margin="0,8,0,0"/>
                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <RadioButton Content="待收款"
                                         IsChecked="{Binding IsPendingPayment}"
                                         GroupName="OrderStatus"
                                         Style="{StaticResource DialogRadioButtonStyle}"
                                         Margin="0,0,20,0"
                                         VerticalAlignment="Center"/>
                            <RadioButton Content="已收款"
                                         IsChecked="{Binding IsPaid}"
                                         GroupName="OrderStatus"
                                         Style="{StaticResource DialogRadioButtonStyle}"
                                         VerticalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Padding="15" Background="#F8F9FA">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="取消"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource GrayButtonStyle}"
                        Width="80"
                        Height="32"
                        Margin="0,0,10,0"/>
                <Button Content="保存"
                        Command="{Binding SaveCommand}"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Width="80"
                        Height="32"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
