using Microsoft.AspNetCore.Mvc;
using Sellsys.Application.Interfaces;
using Sellsys.Application.DTOs.Auth;
using Sellsys.CrossCutting.Common;

namespace Sellsys.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;

        public AuthController(IAuthService authService)
        {
            _authService = authService;
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="request">登录请求</param>
        /// <returns>登录结果</returns>
        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse.Fail("请求参数无效"));
            }

            var response = await _authService.LoginAsync(request.Username, request.Password);
            return new ObjectResult(response) { StatusCode = (int)response.StatusCode };
        }
    }
}
