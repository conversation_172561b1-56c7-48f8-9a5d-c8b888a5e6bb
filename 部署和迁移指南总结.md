# Sellsys 部署和迁移指南总结

## 📋 文档概览

本项目提供了完整的服务器部署和迁移解决方案，包含以下工具和文档：

### 📚 文档文件
1. **服务器迁移教程.md** - 详细的迁移步骤指南
2. **部署和迁移指南总结.md** - 本文档，提供快速参考

### 🛠️ 自动化工具
1. **quick-migration.sh** - Linux/Mac 快速迁移脚本
2. **update-server-address.sh** - Linux/Mac 服务器地址更新工具
3. **update-server-windows.bat** - Windows 服务器地址更新工具

---

## 🚀 快速开始

### 场景1：完整服务器迁移
```bash
# 一键完成从旧服务器到新服务器的完整迁移
./quick-migration.sh full-migration 旧服务器IP 新服务器IP

# 示例
./quick-migration.sh full-migration *********** *************
```

### 场景2：仅更新客户端服务器地址
```bash
# Linux/Mac
./update-server-address.sh 新服务器IP [端口]

# Windows
update-server-windows.bat 新服务器IP [端口]

# 示例
./update-server-address.sh *************
update-server-windows.bat ************* 5000
```

### 场景3：分步骤迁移
```bash
# 步骤1：备份旧服务器
./quick-migration.sh backup 旧服务器IP

# 步骤2：部署到新服务器
./quick-migration.sh deploy 新服务器IP

# 步骤3：更新客户端
./quick-migration.sh update-client 新服务器IP
```

---

## 📁 项目结构说明

```
项目根目录/
├── src/                           # 源代码
│   ├── Sellsys.WebApi/           # 后端API
│   └── Sellsys.WpfClient/        # WPF客户端
├── deploy-package/               # 服务器部署包
│   ├── webapi-selfcontained/     # 自包含API程序
│   ├── deploy.sh                 # 部署脚本
│   └── setup-environment.sh     # 环境配置脚本
├── publish/                      # 编译输出目录
├── backup/                       # 数据备份目录
├── 服务器迁移教程.md              # 详细迁移指南
├── quick-migration.sh            # 快速迁移脚本
├── update-server-address.sh      # 地址更新工具(Linux)
└── update-server-windows.bat     # 地址更新工具(Windows)
```

---

## 🔧 关键配置文件

### 客户端API配置
**文件**: `src/Sellsys.WpfClient/Services/ApiService.cs`
```csharp
// 需要修改的行
private const string ProductionUrl = "http://服务器IP:5000/api";
```

### 服务器配置
**文件**: `deploy-package/webapi-selfcontained/appsettings.json`
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=sellsys.db"
  }
}
```

---

## ⚡ 常用命令速查

### 服务器管理
```bash
# 检查服务状态
sudo systemctl status sellsys-webapi

# 重启服务
sudo systemctl restart sellsys-webapi

# 查看日志
sudo journalctl -u sellsys-webapi -f

# 测试API
curl http://服务器IP:5000/health
```

### 数据库备份
```bash
# 备份数据库
sudo cp /opt/sellsys/sellsys.db /opt/sellsys/backup/sellsys_$(date +%Y%m%d_%H%M%S).db

# 恢复数据库
sudo systemctl stop sellsys-webapi
sudo cp backup/sellsys_backup.db /opt/sellsys/sellsys.db
sudo chown sellsys:sellsys /opt/sellsys/sellsys.db
sudo systemctl start sellsys-webapi
```

### 客户端编译
```bash
# 编译Release版本
dotnet publish src/Sellsys.WpfClient/Sellsys.WpfClient.csproj \
    -c Release \
    -o publish/wpfclient-new \
    --self-contained true \
    -r win-x64
```

---

## 🔍 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查日志
sudo journalctl -u sellsys-webapi --no-pager -n 20

# 检查端口占用
sudo netstat -tlnp | grep :5000

# 检查文件权限
sudo chown -R sellsys:sellsys /opt/sellsys
sudo chmod +x /opt/sellsys/Sellsys.WebApi
```

#### 2. 客户端连接失败
- 检查服务器IP地址是否正确
- 确认服务器防火墙开放5000端口
- 测试网络连通性：`ping 服务器IP`
- 测试API：`curl http://服务器IP:5000/health`

#### 3. 数据丢失
- 检查备份目录：`/opt/sellsys/backup/`
- 恢复最新备份
- 检查数据库文件权限

---

## 📞 技术支持清单

### 迁移前准备
- [ ] 确认新服务器满足系统要求
- [ ] 备份当前服务器所有数据
- [ ] 通知用户迁移时间安排
- [ ] 准备新服务器访问权限

### 迁移过程检查
- [ ] 数据库备份完成
- [ ] 新服务器环境配置完成
- [ ] 应用部署成功
- [ ] 数据恢复完成
- [ ] API接口测试通过
- [ ] 客户端配置更新
- [ ] 新客户端编译成功

### 迁移后验证
- [ ] 服务器运行状态正常
- [ ] 数据完整性检查
- [ ] 用户功能测试
- [ ] 性能监控
- [ ] 备份策略确认

---

## 📈 最佳实践

### 迁移时机
- 选择业务低峰期进行迁移
- 预留充足的时间窗口
- 准备回滚方案

### 数据安全
- 迁移前完整备份
- 验证备份完整性
- 保留多个历史备份

### 用户体验
- 提前通知用户
- 提供详细的更新说明
- 准备技术支持

### 监控和维护
- 设置服务监控
- 定期备份数据
- 监控系统性能

---

## 📝 版本记录

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0 | 2025-07-26 | 初始版本，包含完整迁移方案 |

---

## 📧 联系信息

如需技术支持，请提供以下信息：
- 服务器IP地址和操作系统版本
- 错误信息和日志
- 操作步骤描述
- 系统环境信息

**重要提醒**：在进行任何迁移操作前，请务必备份所有重要数据！
