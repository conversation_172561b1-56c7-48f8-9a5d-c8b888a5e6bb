using Sellsys.WpfClient.Commands;
using Sellsys.WpfClient.Models;
using Sellsys.WpfClient.Services;
using Sellsys.WpfClient.ViewModels.Base;
using System.Collections.ObjectModel;
using System.Windows.Input;
using System.Windows;

namespace Sellsys.WpfClient.ViewModels
{
    /// <summary>
    /// 财务管理ViewModel - 重构版本，适配原型图需求
    /// </summary>
    public class FinanceManagementViewModel : FrontendPaginatedViewModelBase<FinanceOrderDetail>
    {
        private readonly ApiService _apiService;

        // 主要数据集合
        private ObservableCollection<FinanceOrderDetail> _financeOrderDetails;
        private FinanceOrderDetail? _selectedOrderDetail;
        private FinanceOrderSummary? _summary;
        private bool _isLoading;

        // 筛选相关
        private FinanceFilter _filter;
        private FinanceFilterOptions? _filterOptions;

        // 分页相关属性现在从基类继承

        // 搜索
        private string _searchKeyword = string.Empty;
        private string _searchCustomerName = string.Empty;

        #region 属性

        /// <summary>
        /// 财务订单明细列表
        /// </summary>
        public ObservableCollection<FinanceOrderDetail> FinanceOrderDetails
        {
            get => _financeOrderDetails;
            set => SetProperty(ref _financeOrderDetails, value);
        }

        /// <summary>
        /// 选中的订单明细
        /// </summary>
        public FinanceOrderDetail? SelectedOrderDetail
        {
            get => _selectedOrderDetail;
            set => SetProperty(ref _selectedOrderDetail, value);
        }

        /// <summary>
        /// 财务汇总信息
        /// </summary>
        public FinanceOrderSummary? Summary
        {
            get => _summary;
            set => SetProperty(ref _summary, value);
        }

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// 筛选条件
        /// </summary>
        public FinanceFilter Filter
        {
            get => _filter;
            set => SetProperty(ref _filter, value);
        }

        /// <summary>
        /// 筛选数据源
        /// </summary>
        public FinanceFilterOptions? FilterOptions
        {
            get => _filterOptions;
            set => SetProperty(ref _filterOptions, value);
        }

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string SearchKeyword
        {
            get => _searchKeyword;
            set
            {
                SetProperty(ref _searchKeyword, value);
                Filter.SearchKeyword = value;
            }
        }

        /// <summary>
        /// 输入客户名称
        /// </summary>
        public string SearchCustomerName
        {
            get => _searchCustomerName;
            set => SetProperty(ref _searchCustomerName, value);
        }

        /// <summary>
        /// 生效日期选项
        /// </summary>
        public List<string> EffectiveDateOptions { get; } = new List<string>
        {
            "全部", "本月", "上月", "本季度", "上季度", "本年", "去年"
        };

        /// <summary>
        /// 到期日期选项
        /// </summary>
        public List<string> ExpiryDateOptions { get; } = new List<string>
        {
            "全部", "本月", "上月", "本季度", "上季度", "本年", "去年"
        };

        /// <summary>
        /// 签单日期选项
        /// </summary>
        public List<string> CreatedDateOptions { get; } = new List<string>
        {
            "全部", "本月", "上月", "本季度", "上季度", "本年", "去年"
        };

        /// <summary>
        /// 选中的生效日期
        /// </summary>
        public string? SelectedEffectiveDate { get; set; }

        /// <summary>
        /// 选中的到期日期
        /// </summary>
        public string? SelectedExpiryDate { get; set; }

        /// <summary>
        /// 选中的签单日期
        /// </summary>
        public string? SelectedCreatedDate { get; set; }



        // 分页属性现在从基类继承

        // 筛选条件的便捷属性
        /// <summary>
        /// 选中的客户
        /// </summary>
        public FilterOption? SelectedCustomer
        {
            get => FilterOptions?.Customers.FirstOrDefault(c => c.Value == Filter.CustomerId?.ToString());
            set
            {
                Filter.CustomerId = value != null && int.TryParse(value.Value, out var id) ? id : null;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 选中的产品
        /// </summary>
        public FilterOption? SelectedProduct
        {
            get => FilterOptions?.Products.FirstOrDefault(p => p.Value == Filter.ProductId?.ToString());
            set
            {
                Filter.ProductId = value != null && int.TryParse(value.Value, out var id) ? id : null;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 选中的负责人
        /// </summary>
        public FilterOption? SelectedSalesPerson
        {
            get => FilterOptions?.SalesPersons.FirstOrDefault(s => s.Value == Filter.SalesPersonId?.ToString());
            set
            {
                Filter.SalesPersonId = value != null && int.TryParse(value.Value, out var id) ? id : null;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 选中的订单状态
        /// </summary>
        public FilterOption? SelectedOrderStatus
        {
            get => FilterOptions?.OrderStatuses.FirstOrDefault(s => s.Value == Filter.OrderStatus);
            set
            {
                Filter.OrderStatus = value?.Value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 生效日期开始
        /// </summary>
        public DateTime? EffectiveDateStart
        {
            get => Filter.EffectiveDateStart;
            set
            {
                Filter.EffectiveDateStart = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 生效日期结束
        /// </summary>
        public DateTime? EffectiveDateEnd
        {
            get => Filter.EffectiveDateEnd;
            set
            {
                Filter.EffectiveDateEnd = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 到期日期开始
        /// </summary>
        public DateTime? ExpiryDateStart
        {
            get => Filter.ExpiryDateStart;
            set
            {
                Filter.ExpiryDateStart = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 到期日期结束
        /// </summary>
        public DateTime? ExpiryDateEnd
        {
            get => Filter.ExpiryDateEnd;
            set
            {
                Filter.ExpiryDateEnd = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 支付日期开始
        /// </summary>
        public DateTime? PaymentDateStart
        {
            get => Filter.PaymentDateStart;
            set
            {
                Filter.PaymentDateStart = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 支付日期结束
        /// </summary>
        public DateTime? PaymentDateEnd
        {
            get => Filter.PaymentDateEnd;
            set
            {
                Filter.PaymentDateEnd = value;
                OnPropertyChanged();
            }
        }

        // 分页相关属性现在从基类继承

        #endregion

        #region 命令

        public ICommand LoadDataCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ResetFiltersCommand { get; }
        public ICommand EditPaymentCommand { get; }
        public ICommand ConfirmPaymentCommand { get; }
        // RefreshCommand, PreviousPageCommand, NextPageCommand, GoToPageCommand 现在从基类继承

        #endregion

        #region 构造函数

        public FinanceManagementViewModel()
        {
            _apiService = new ApiService();
            _financeOrderDetails = new ObservableCollection<FinanceOrderDetail>();
            _filter = new FinanceFilter();

            // 初始化空的筛选选项，避免界面绑定失败
            _filterOptions = new FinanceFilterOptions
            {
                Customers = new List<FilterOption>(),
                Products = new List<FilterOption>(),
                SalesPersons = new List<FilterOption>(),
                OrderStatuses = new List<FilterOption>()
            };

            // 初始化命令
            LoadDataCommand = new AsyncRelayCommand(async p => await LoadDataAsync());
            SearchCommand = new AsyncRelayCommand(async p => await SearchAsync());
            ResetFiltersCommand = new RelayCommand(p => ResetFilters());
            EditPaymentCommand = new AsyncRelayCommand(async p => await EditPaymentAsync(), p => SelectedOrderDetail?.CanEditPayment == true);
            ConfirmPaymentCommand = new AsyncRelayCommand(async p => await ConfirmPaymentAsync(), p => SelectedOrderDetail?.OrderStatus == "待收款");
            // RefreshCommand, PreviousPageCommand, NextPageCommand, GoToPageCommand 从基类继承
        }

        #endregion

        #region 方法实现

        public override async Task LoadDataAsync()
        {
            if (IsDataLoaded) return;
            await LoadFilterOptionsAsync();
            await LoadFinanceDataAsync();
            IsDataLoaded = true;
        }

        /// <summary>
        /// 加载筛选数据源
        /// </summary>
        private async Task LoadFilterOptionsAsync()
        {
            try
            {
                FilterOptions = await _apiService.GetFinanceFilterOptionsAsync();
            }
            catch (Exception ex)
            {
                // 提供默认的空筛选选项，避免界面绑定失败
                FilterOptions = new FinanceFilterOptions
                {
                    Customers = new List<FilterOption>(),
                    Products = new List<FilterOption>(),
                    SalesPersons = new List<FilterOption>(),
                    OrderStatuses = new List<FilterOption>()
                };

                MessageBox.Show($"加载筛选数据源失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载财务数据 - 现在使用基类的分页机制
        /// </summary>
        private async Task LoadFinanceDataAsync()
        {
            // 清空缓存数据，强制重新加载
            AllData.Clear();
            // 使用基类的分页加载机制
            await base.LoadDataAsync();
        }

        /// <summary>
        /// 搜索
        /// </summary>
        private async Task SearchAsync()
        {
            Filter.PageNumber = 1; // 重置到第一页
            await LoadFinanceDataAsync();
        }

        /// <summary>
        /// 重置筛选条件
        /// </summary>
        private void ResetFilters()
        {
            Filter.Reset();
            SearchKeyword = string.Empty;

            // 通知所有筛选相关属性更新
            OnPropertyChanged(nameof(SelectedCustomer));
            OnPropertyChanged(nameof(SelectedProduct));
            OnPropertyChanged(nameof(SelectedSalesPerson));
            OnPropertyChanged(nameof(SelectedOrderStatus));
            OnPropertyChanged(nameof(EffectiveDateStart));
            OnPropertyChanged(nameof(EffectiveDateEnd));
            OnPropertyChanged(nameof(ExpiryDateStart));
            OnPropertyChanged(nameof(ExpiryDateEnd));
            OnPropertyChanged(nameof(PaymentDateStart));
            OnPropertyChanged(nameof(PaymentDateEnd));
        }

        /// <summary>
        /// 编辑收款信息
        /// </summary>
        private async Task EditPaymentAsync()
        {
            if (SelectedOrderDetail == null) return;

            try
            {
                // 创建对话框ViewModel
                var dialogViewModel = new ViewModels.Dialogs.EditPaymentInfoDialogViewModel(SelectedOrderDetail);

                // 创建并显示对话框
                var dialog = new Views.Dialogs.EditPaymentInfoDialog(dialogViewModel);
                dialog.Owner = Application.Current.MainWindow;

                var result = dialog.ShowDialog();

                // 如果保存成功，刷新数据
                if (result == true)
                {
                    await RefreshAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开编辑对话框失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 确认收款
        /// </summary>
        private async Task ConfirmPaymentAsync()
        {
            if (SelectedOrderDetail == null) return;

            try
            {
                // 确认对话框
                var result = MessageBox.Show(
                    $"确认收款订单：{SelectedOrderDetail.OrderNumber}？\n收款后状态将变更为\"已收款\"。",
                    "确认收款",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes) return;

                // 调用API确认收款
                await _apiService.ConfirmPaymentAsync(SelectedOrderDetail.OrderId, DateTime.Now);

                // 显示成功消息
                MessageBox.Show("收款确认成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);

                // 刷新数据
                await RefreshAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"确认收款失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 刷新数据
        /// </summary>
        private async Task RefreshAsync()
        {
            await LoadFinanceDataAsync();
        }

        // 分页方法现在从基类继承

        #endregion

        #region 基类抽象方法实现

        protected override async Task<List<FinanceOrderDetail>> LoadAllDataFromApiAsync()
        {
            try
            {
                var result = await _apiService.GetFinanceOrderDetailsAsync(Filter);
                Summary = result.Summary;
                return result.Items;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载财务数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return new List<FinanceOrderDetail>();
            }
        }

        protected override List<FinanceOrderDetail> FilterData(List<FinanceOrderDetail> data, string searchKeyword)
        {
            if (string.IsNullOrWhiteSpace(searchKeyword))
                return data;

            return data.Where(item =>
                item.CustomerName?.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) == true ||
                item.ProductName?.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) == true ||
                item.SalesPersonName?.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) == true
            ).ToList();
        }

        protected override void OnDataLoaded(List<FinanceOrderDetail> data)
        {
            FinanceOrderDetails.Clear();
            foreach (var item in data)
            {
                FinanceOrderDetails.Add(item);
            }
        }

        #endregion
    }
}