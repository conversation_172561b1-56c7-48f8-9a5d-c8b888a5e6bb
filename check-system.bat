@echo off
echo ========================================
echo           System Check Tool
echo ========================================
echo.

cd /d "%~dp0"

echo [%time%] Starting system check...
echo.

REM Check .NET SDK
echo 1. Checking .NET SDK...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo [FAIL] .NET SDK not installed
    echo        Download from: https://dotnet.microsoft.com/download/dotnet/8.0
) else (
    echo [PASS] .NET SDK installed
    dotnet --version
)
echo.

REM Check project files
echo 2. Checking project files...
if exist "src\Sellsys.WebApi\Sellsys.WebApi.csproj" (
    echo [PASS] Backend project file exists
) else (
    echo [FAIL] Backend project file missing
)

if exist "src\Sellsys.WpfClient\Sellsys.WpfClient.csproj" (
    echo [PASS] Frontend project file exists
) else (
    echo [FAIL] Frontend project file missing
)
echo.

REM Check config files
echo 3. Checking configuration files...
if exist "src\Sellsys.WebApi\appsettings.json" (
    echo [PASS] Backend config exists
) else (
    echo [FAIL] Backend config missing
)

if exist "src\Sellsys.WpfClient\appsettings.json" (
    echo [PASS] Frontend config exists
) else (
    echo [FAIL] Frontend config missing
)
echo.

REM Check database
echo 4. Checking database...
if exist "src\Sellsys.WebApi\sellsys.db" (
    echo [PASS] Database file exists
) else (
    echo [INFO] Database will be created on first run
)
echo.

REM Check port
echo 5. Checking port 5078...
netstat -an | findstr ":5078" >nul
if errorlevel 1 (
    echo [PASS] Port 5078 is available
) else (
    echo [WARN] Port 5078 is in use
)
echo.

REM Try building
echo 6. Testing build...
echo    Building backend...
dotnet build src\Sellsys.WebApi --verbosity quiet >nul 2>&1
if errorlevel 1 (
    echo [FAIL] Backend build failed
) else (
    echo [PASS] Backend build successful
)

echo    Building frontend...
dotnet build src\Sellsys.WpfClient --verbosity quiet >nul 2>&1
if errorlevel 1 (
    echo [FAIL] Frontend build failed
) else (
    echo [PASS] Frontend build successful
)
echo.

REM Check scripts
echo 7. Checking startup scripts...
if exist "start-backend.bat" (
    echo [PASS] Backend startup script exists
) else (
    echo [FAIL] Backend startup script missing
)

if exist "start-frontend.bat" (
    echo [PASS] Frontend startup script exists
) else (
    echo [FAIL] Frontend startup script missing
)

if exist "start-system.bat" (
    echo [PASS] System startup script exists
) else (
    echo [FAIL] System startup script missing
)
echo.

echo ========================================
echo           Check Complete
echo ========================================
echo.
echo If all checks pass, run start-system.bat to start the application
echo.
pause
