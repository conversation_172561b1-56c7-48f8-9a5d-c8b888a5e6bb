<Window x:Class="Sellsys.WpfClient.Views.Dialogs.EditDepartmentDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="编辑部门" Height="220" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0" Text="编辑部门信息" 
                   FontSize="16" FontWeight="Bold" 
                   Foreground="#333333" 
                   Margin="0,0,0,20"/>

        <!-- Form Content -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="120"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Department Name -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="部门名称:"
                       VerticalAlignment="Center"
                       Margin="0,0,15,15"
                       FontWeight="Medium"
                       FontSize="14"/>
            <TextBox x:Name="DepartmentNameTextBox"
                     Grid.Row="0" Grid.Column="1"
                     Text="{Binding DepartmentName, UpdateSourceTrigger=PropertyChanged}"
                     Height="32"
                     Padding="10,6"
                     BorderBrush="#CCCCCC"
                     BorderThickness="1"
                     FontSize="14"
                     Margin="0,0,0,15"/>
        </Grid>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,20,0,0">
            <Button Content="保存" 
                    Command="{Binding SaveCommand}"
                    Style="{StaticResource PrimaryButtonStyle}"
                    Width="80" Height="35" 
                    Margin="0,0,10,0"/>
            <Button Content="取消" 
                    Command="{Binding CancelCommand}"
                    Style="{StaticResource SecondaryButtonStyle}"
                    Width="80" Height="35"/>
        </StackPanel>

        <!-- Loading Overlay -->
        <Border Grid.RowSpan="3" 
                Background="#80000000" 
                Visibility="{Binding IsSaving, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="正在保存..." Foreground="White" HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
