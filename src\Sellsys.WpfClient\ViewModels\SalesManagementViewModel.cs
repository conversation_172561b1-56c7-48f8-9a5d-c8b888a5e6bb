using Sellsys.WpfClient.Commands;
using Sellsys.WpfClient.Models;
using Sellsys.WpfClient.Services;
using Sellsys.WpfClient.ViewModels.Base;
using Sellsys.WpfClient.Views.Dialogs;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows;

namespace Sellsys.WpfClient.ViewModels
{
    public class SalesManagementViewModel : FrontendPaginatedViewModelBase<Customer>
    {
        private readonly ApiService _apiService;
        private ObservableCollection<Customer> _customers;
        private ObservableCollection<Employee> _employees;
        private Customer? _selectedCustomer;
        private string _searchText = string.Empty;

        private bool _isAllSelected;

        // Filter properties
        private ObservableCollection<string> _industryTypes;
        private ObservableCollection<string> _provinces;
        private ObservableCollection<string> _cities;
        private ObservableCollection<string> _contactStatuses;
        private ObservableCollection<string> _responsiblePersons;
        private string _selectedIndustryType = "全部";
        private string _selectedProvince = "全部";
        private string _selectedCity = "全部";
        private string _selectedContactStatus = "全部";
        private string _selectedResponsiblePerson = "全部";
        private DateTime? _selectedAppointmentDateFilter;

        public ObservableCollection<Customer> Customers
        {
            get => _customers;
            set => SetProperty(ref _customers, value);
        }

        public ObservableCollection<Employee> Employees
        {
            get => _employees;
            set => SetProperty(ref _employees, value);
        }

        public Customer? SelectedCustomer
        {
            get => _selectedCustomer;
            set => SetProperty(ref _selectedCustomer, value);
        }

        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        public bool IsAllSelected
        {
            get => _isAllSelected;
            set => SetProperty(ref _isAllSelected, value);
        }

        // Filter collections
        public ObservableCollection<string> IndustryTypes
        {
            get => _industryTypes;
            set => SetProperty(ref _industryTypes, value);
        }

        public ObservableCollection<string> Provinces
        {
            get => _provinces;
            set => SetProperty(ref _provinces, value);
        }

        public ObservableCollection<string> Cities
        {
            get => _cities;
            set => SetProperty(ref _cities, value);
        }

        public ObservableCollection<string> ContactStatuses
        {
            get => _contactStatuses;
            set => SetProperty(ref _contactStatuses, value);
        }

        public ObservableCollection<string> ResponsiblePersons
        {
            get => _responsiblePersons;
            set => SetProperty(ref _responsiblePersons, value);
        }

        // Filter selections
        public string SelectedIndustryType
        {
            get => _selectedIndustryType;
            set => SetProperty(ref _selectedIndustryType, value);
        }

        public string SelectedProvince
        {
            get => _selectedProvince;
            set => SetProperty(ref _selectedProvince, value);
        }

        public string SelectedCity
        {
            get => _selectedCity;
            set => SetProperty(ref _selectedCity, value);
        }

        public string SelectedContactStatus
        {
            get => _selectedContactStatus;
            set => SetProperty(ref _selectedContactStatus, value);
        }

        public string SelectedResponsiblePerson
        {
            get => _selectedResponsiblePerson;
            set => SetProperty(ref _selectedResponsiblePerson, value);
        }

        public DateTime? SelectedAppointmentDateFilter
        {
            get => _selectedAppointmentDateFilter;
            set => SetProperty(ref _selectedAppointmentDateFilter, value);
        }

        // Commands
        public ICommand LoadDataCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ClearFiltersCommand { get; }
        public ICommand ViewContactsCommand { get; }
        public ICommand EditCustomerCommand { get; }
        public ICommand ViewDetailsCommand { get; }
        public ICommand SelectAllCommand { get; }
        public ICommand ViewContactRecordsCommand { get; }
        public ICommand ViewOrdersCommand { get; }
        public ICommand ViewOrderRecordsCommand { get; }

        public SalesManagementViewModel()
        {
            _apiService = new ApiService();
            _customers = new ObservableCollection<Customer>();
            _employees = new ObservableCollection<Employee>();
            _industryTypes = new ObservableCollection<string>();
            _provinces = new ObservableCollection<string>();
            _cities = new ObservableCollection<string>();
            _contactStatuses = new ObservableCollection<string>();
            _responsiblePersons = new ObservableCollection<string>();

            // Initialize commands
            LoadDataCommand = new AsyncRelayCommand(async p => await LoadCustomersAsync());
            SearchCommand = new AsyncRelayCommand(async p => await SearchCustomersAsync());
            ClearFiltersCommand = new RelayCommand(p => ClearFilters());
            ViewContactsCommand = new RelayCommand(p => ViewContacts(p as Customer));
            EditCustomerCommand = new RelayCommand(p => EditCustomer(p as Customer));
            ViewDetailsCommand = new RelayCommand(p => ViewDetails(p as Customer));
            SelectAllCommand = new RelayCommand(p => ToggleSelectAll());
            ViewContactRecordsCommand = new RelayCommand(p => ViewContactRecords(p as Customer));
            ViewOrdersCommand = new RelayCommand(p => ViewOrders(p as Customer));
            ViewOrderRecordsCommand = new RelayCommand(p => ViewOrderRecords(p as Customer));

            // Initialize filter data
            InitializeFilters();

            // 订阅客户更新事件
            EventBus.Instance.Subscribe<CustomerUpdatedEvent>(OnCustomerUpdated);
            EventBus.Instance.Subscribe<CustomerAssignedEvent>(OnCustomerAssigned);

            // 订阅员工更新事件
            EventBus.Instance.Subscribe<EmployeeUpdatedEvent>(OnEmployeeUpdated);

            // Note: Data loading is now triggered manually or when view becomes active
            // This prevents API calls during application startup
        }

        public override async Task LoadDataAsync()
        {
            if (IsDataLoaded) return; // Avoid loading data multiple times
            await LoadCustomersAsync();
            IsDataLoaded = true;
        }

        /// <summary>
        /// 手动刷新数据
        /// </summary>
        private async Task RefreshDataAsync()
        {
            try
            {
                // 强制重新加载数据
                IsDataLoaded = false;
                await LoadCustomersAsync();
                IsDataLoaded = true;

                System.Diagnostics.Debug.WriteLine("SalesManagement: 手动刷新数据完成");
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleApiError(ex, "refreshing customer data");
            }
        }

        private async Task LoadCustomersAsync()
        {
            try
            {
                // 使用基类的分页加载功能
                await base.LoadDataAsync();

                // Load reference data
                var employeesTask = _apiService.GetEmployeesAsync();
                await employeesTask;

                Employees.Clear();
                foreach (var employee in employeesTask.Result)
                {
                    Employees.Add(employee);
                }

                // Update filter options based on loaded data
                UpdateFilterOptions();
            }
            catch (Exception ex)
            {
                // Use the error handling service
                ErrorHandlingService.HandleApiError(ex, "loading customer data");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task EnrichCustomerWithFollowUpData(Customer customer)
        {
            try
            {
                // Get latest follow-up log for this customer to get next contact date and customer intention
                // 传递当前用户ID以进行权限控制
                var currentUserId = CurrentUser.User?.Id;
                var followUpLogs = await _apiService.GetSalesFollowUpLogsByCustomerIdAsync(customer.Id, currentUserId);
                var latestLog = followUpLogs
                    .OrderByDescending(log => log.CreatedAt)
                    .FirstOrDefault();

                if (latestLog != null)
                {
                    customer.NextContactDate = latestLog.NextFollowUpDate;
                    customer.CustomerIntention = latestLog.CustomerIntention ?? "待分配";
                }
                else
                {
                    // 如果没有跟进记录，设置默认值
                    customer.CustomerIntention = "待分配";
                }
            }
            catch (Exception ex)
            {
                // Log error but don't fail the whole operation
                System.Diagnostics.Debug.WriteLine($"Error enriching customer {customer.Id}: {ex.Message}");
                // 设置默认值以防出错
                customer.CustomerIntention = "待分配";
            }
        }

        private async Task SearchCustomersAsync()
        {
            // 使用新的分页搜索功能
            await SearchAsync(SearchText);
        }

        private void InitializeFilters()
        {
            // Initialize industry types
            IndustryTypes.Clear();
            IndustryTypes.Add("全部");
            IndustryTypes.Add("教育");
            IndustryTypes.Add("医疗");
            IndustryTypes.Add("金融");
            IndustryTypes.Add("制造业");
            IndustryTypes.Add("服务业");
            IndustryTypes.Add("政府");
            IndustryTypes.Add("其他");

            // Initialize provinces
            Provinces.Clear();
            Provinces.Add("全部");
            Provinces.Add("四川");
            Provinces.Add("广西");
            Provinces.Add("广东");
            Provinces.Add("北京");
            Provinces.Add("上海");
            Provinces.Add("重庆");
            Provinces.Add("天津");

            // Initialize cities
            Cities.Clear();
            Cities.Add("全部");
            Cities.Add("成都");
            Cities.Add("桂林");
            Cities.Add("广州");
            Cities.Add("深圳");

            // Initialize contact statuses
            ContactStatuses.Clear();
            ContactStatuses.Add("全部");
            ContactStatuses.Add("待跟进");
            ContactStatuses.Add("跟进中");
            ContactStatuses.Add("已成交");
            ContactStatuses.Add("已流失");

            // Initialize responsible persons - will be loaded from API
            ResponsiblePersons.Clear();
            ResponsiblePersons.Add("全部");

            // Set default selections
            SelectedIndustryType = "全部";
            SelectedProvince = "全部";
            SelectedCity = "全部";
            SelectedContactStatus = "全部";
            SelectedResponsiblePerson = "全部";
        }

        private void UpdateFilterOptions()
        {
            // Update provinces based on loaded customers
            var provinces = Customers.Where(c => !string.IsNullOrEmpty(c.Province))
                                   .Select(c => c.Province!)
                                   .Distinct()
                                   .OrderBy(p => p)
                                   .ToList();

            Provinces.Clear();
            Provinces.Add("全部");
            foreach (var province in provinces)
            {
                Provinces.Add(province);
            }

            // Update cities based on loaded customers
            var cities = Customers.Where(c => !string.IsNullOrEmpty(c.City))
                                .Select(c => c.City!)
                                .Distinct()
                                .OrderBy(c => c)
                                .ToList();

            Cities.Clear();
            Cities.Add("全部");
            foreach (var city in cities)
            {
                Cities.Add(city);
            }

            // Update responsible persons based on loaded employees - only show sales staff
            ResponsiblePersons.Clear();
            ResponsiblePersons.Add("全部");
            // Filter to only show employees with role "销售"
            var salesEmployees = Employees.Where(e => e.RoleName == "销售").OrderBy(e => e.Name);
            foreach (var employee in salesEmployees)
            {
                ResponsiblePersons.Add(employee.Name);
            }
        }

        private void ViewContacts(Customer? customer)
        {
            if (customer == null) return;

            try
            {
                var dialog = new ViewContactsDialog();
                var viewModel = new ViewContactsDialogViewModel(customer);
                dialog.DataContext = viewModel;

                // Set owner to main window for proper positioning
                dialog.Owner = Application.Current.MainWindow;

                viewModel.CloseRequested += (sender, args) =>
                {
                    dialog.Close();
                };

                dialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开查看联系人对话框失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditCustomer(Customer? customer)
        {
            if (customer == null) return;

            // TODO: Show EditCustomerDialog
            MessageBox.Show($"编辑客户: {customer.Name}", "编辑客户", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ViewDetails(Customer? customer)
        {
            if (customer == null) return;

            // TODO: Show CustomerDetailsDialog
            MessageBox.Show($"查看客户详情: {customer.Name}", "客户详情", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ClearFilters()
        {
            SelectedIndustryType = "全部";
            SelectedProvince = "全部";
            SelectedCity = "全部";
            SelectedContactStatus = "全部";
            SelectedResponsiblePerson = "全部";
            SelectedAppointmentDateFilter = null;
            SearchText = string.Empty;
        }

        private void ToggleSelectAll()
        {
            // 检查当前是否所有项都已选中
            bool allSelected = Customers.All(c => c.IsSelected);

            // 如果全部选中，则取消全选；否则全选
            IsAllSelected = !allSelected;

            foreach (var customer in Customers)
            {
                customer.IsSelected = IsAllSelected;
            }
        }

        private void ViewContactRecords(Customer? customer)
        {
            if (customer == null) return;

            try
            {
                var dialog = new ContactRecordsDialog();
                var viewModel = new ContactRecordsDialogViewModel(customer);
                dialog.DataContext = viewModel;

                // Set owner to main window for proper positioning
                dialog.Owner = Application.Current.MainWindow;

                viewModel.CloseRequested += (sender, args) =>
                {
                    dialog.Close();
                };

                // 监听记录变更事件，用于刷新主界面数据
                dialog.Closed += async (sender, args) =>
                {
                    // 重新加载客户数据以更新联系记录数量
                    await LoadCustomersAsync();
                };

                dialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开联系记录对话框失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewOrders(Customer? customer)
        {
            if (customer == null) return;

            // 调用订单记录对话框
            ViewOrderRecords(customer);
        }

        private void ViewOrderRecords(Customer? customer)
        {
            if (customer == null) return;

            try
            {
                var dialog = new OrderRecordsDialog();
                var viewModel = new OrderRecordsDialogViewModel(customer);
                dialog.DataContext = viewModel;

                // Set owner to main window for proper positioning
                dialog.Owner = Application.Current.MainWindow;

                viewModel.CloseRequested += (sender, args) =>
                {
                    dialog.Close();
                };

                // 监听记录变更事件，用于刷新主界面数据
                dialog.Closed += async (sender, args) =>
                {
                    // 重新加载客户数据以更新订单数量
                    await LoadCustomersAsync();
                };

                dialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开订单记录对话框失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 处理客户更新事件
        /// </summary>
        private async void OnCustomerUpdated(CustomerUpdatedEvent eventData)
        {
            try
            {
                // 如果数据已加载，则刷新数据以显示最新的客户信息
                if (IsDataLoaded)
                {
                    System.Diagnostics.Debug.WriteLine($"SalesManagement: 收到客户更新事件 - {eventData.UpdateType}: {eventData.CustomerName}");

                    // 重置数据加载标志，强制重新加载
                    IsDataLoaded = false;
                    await LoadCustomersAsync();
                    IsDataLoaded = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理客户更新事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理客户分配事件
        /// </summary>
        private async void OnCustomerAssigned(CustomerAssignedEvent eventData)
        {
            try
            {
                // 如果数据已加载，则刷新数据以显示最新的分配信息
                if (IsDataLoaded)
                {
                    System.Diagnostics.Debug.WriteLine($"SalesManagement: 收到客户分配事件 - {eventData.AssignmentType}: {eventData.CustomerName}");

                    // 重置数据加载标志，强制重新加载
                    IsDataLoaded = false;
                    await LoadCustomersAsync();
                    IsDataLoaded = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理客户分配事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理员工更新事件
        /// </summary>
        private async void OnEmployeeUpdated(EmployeeUpdatedEvent eventData)
        {
            try
            {
                // 如果数据已加载，则刷新数据以显示最新的员工信息
                if (IsDataLoaded)
                {
                    System.Diagnostics.Debug.WriteLine($"SalesManagement: 收到员工更新事件 - {eventData.UpdateType}: {eventData.EmployeeName}");

                    // 重置数据加载标志，强制重新加载
                    IsDataLoaded = false;
                    await LoadCustomersAsync();
                    IsDataLoaded = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理员工更新事件失败: {ex.Message}");
            }
        }

        // 注意：权限控制已移至后端API，前端不再进行权限过滤
        // 所有客户数据的权限控制都由后端CustomerService.GetCustomersWithPermissionAsync()方法处理
        #region Abstract Methods Implementation
        /// <summary>
        /// 从API加载所有客户数据
        /// </summary>
        protected override async Task<List<Customer>> LoadAllDataFromApiAsync()
        {
            var customers = await _apiService.GetCustomersAsync();
            return customers?.OrderByDescending(c => c.CreatedAt).ToList() ?? new List<Customer>();
        }

        /// <summary>
        /// 过滤客户数据（包含搜索关键词和筛选条件）
        /// </summary>
        protected override List<Customer> FilterData(List<Customer> data, string searchKeyword)
        {
            var filteredData = data.AsEnumerable();

            // 应用筛选条件
            // Industry type filter
            if (!string.IsNullOrEmpty(SelectedIndustryType) && SelectedIndustryType != "全部")
            {
                filteredData = filteredData.Where(c =>
                    c.IndustryTypes?.Contains(SelectedIndustryType, StringComparison.OrdinalIgnoreCase) == true);
            }

            // Province filter
            if (!string.IsNullOrEmpty(SelectedProvince) && SelectedProvince != "全部")
            {
                filteredData = filteredData.Where(c =>
                    c.Province?.Equals(SelectedProvince, StringComparison.OrdinalIgnoreCase) == true);
            }

            // City filter
            if (!string.IsNullOrEmpty(SelectedCity) && SelectedCity != "全部")
            {
                filteredData = filteredData.Where(c =>
                    c.City?.Equals(SelectedCity, StringComparison.OrdinalIgnoreCase) == true);
            }

            // Contact status filter
            if (!string.IsNullOrEmpty(SelectedContactStatus) && SelectedContactStatus != "全部")
            {
                filteredData = filteredData.Where(c =>
                    c.CustomerIntention?.Equals(SelectedContactStatus, StringComparison.OrdinalIgnoreCase) == true);
            }

            // Responsible person filter
            if (!string.IsNullOrEmpty(SelectedResponsiblePerson) && SelectedResponsiblePerson != "全部")
            {
                filteredData = filteredData.Where(c =>
                    c.ResponsiblePersonName?.Equals(SelectedResponsiblePerson, StringComparison.OrdinalIgnoreCase) == true);
            }

            // Appointment date filter (预约日期筛选)
            if (SelectedAppointmentDateFilter.HasValue)
            {
                var filterDate = SelectedAppointmentDateFilter.Value.Date;
                filteredData = filteredData.Where(c =>
                    c.NextContactDate.HasValue && c.NextContactDate.Value.Date == filterDate);
            }

            // Text search filter
            if (!string.IsNullOrWhiteSpace(searchKeyword))
            {
                filteredData = filteredData.Where(c =>
                    c.Name.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) ||
                    (!string.IsNullOrEmpty(c.PrimaryContactName) && c.PrimaryContactName.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(c.PrimaryContactPhone) && c.PrimaryContactPhone.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(c.Province) && c.Province.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(c.City) && c.City.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(c.Address) && c.Address.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(c.SalesPersonName) && c.SalesPersonName.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(c.SupportPersonName) && c.SupportPersonName.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase))
                );
            }

            return filteredData.ToList();
        }

        /// <summary>
        /// 数据加载完成时更新UI集合
        /// </summary>
        protected override async void OnDataLoaded(List<Customer> data)
        {
            Customers.Clear();
            foreach (var customer in data)
            {
                // 丰富客户数据
                await EnrichCustomerWithFollowUpData(customer);
                Customers.Add(customer);
            }
        }
        #endregion
    }
}
