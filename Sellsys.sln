﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sellsys.WebApi", "src\Sellsys.WebApi\Sellsys.WebApi.csproj", "{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sellsys.Application", "src\Sellsys.Application\Sellsys.Application.csproj", "{A1D52FFF-BE93-4424-84DE-1F4A9284E306}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sellsys.Domain", "src\Sellsys.Domain\Sellsys.Domain.csproj", "{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sellsys.Infrastructure", "src\Sellsys.Infrastructure\Sellsys.Infrastructure.csproj", "{340F6024-40FF-46CF-BD07-16984D6E503B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sellsys.WpfClient", "src\Sellsys.WpfClient\Sellsys.WpfClient.csproj", "{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sellsys.CrossCutting", "src\Sellsys.CrossCutting\Sellsys.CrossCutting.csproj", "{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4}.Debug|x64.Build.0 = Debug|Any CPU
		{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4}.Debug|x86.Build.0 = Debug|Any CPU
		{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4}.Release|Any CPU.Build.0 = Release|Any CPU
		{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4}.Release|x64.ActiveCfg = Release|Any CPU
		{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4}.Release|x64.Build.0 = Release|Any CPU
		{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4}.Release|x86.ActiveCfg = Release|Any CPU
		{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4}.Release|x86.Build.0 = Release|Any CPU
		{A1D52FFF-BE93-4424-84DE-1F4A9284E306}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1D52FFF-BE93-4424-84DE-1F4A9284E306}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1D52FFF-BE93-4424-84DE-1F4A9284E306}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A1D52FFF-BE93-4424-84DE-1F4A9284E306}.Debug|x64.Build.0 = Debug|Any CPU
		{A1D52FFF-BE93-4424-84DE-1F4A9284E306}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A1D52FFF-BE93-4424-84DE-1F4A9284E306}.Debug|x86.Build.0 = Debug|Any CPU
		{A1D52FFF-BE93-4424-84DE-1F4A9284E306}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1D52FFF-BE93-4424-84DE-1F4A9284E306}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1D52FFF-BE93-4424-84DE-1F4A9284E306}.Release|x64.ActiveCfg = Release|Any CPU
		{A1D52FFF-BE93-4424-84DE-1F4A9284E306}.Release|x64.Build.0 = Release|Any CPU
		{A1D52FFF-BE93-4424-84DE-1F4A9284E306}.Release|x86.ActiveCfg = Release|Any CPU
		{A1D52FFF-BE93-4424-84DE-1F4A9284E306}.Release|x86.Build.0 = Release|Any CPU
		{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658}.Debug|x64.Build.0 = Debug|Any CPU
		{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658}.Debug|x86.ActiveCfg = Debug|Any CPU
		{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658}.Debug|x86.Build.0 = Debug|Any CPU
		{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658}.Release|Any CPU.Build.0 = Release|Any CPU
		{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658}.Release|x64.ActiveCfg = Release|Any CPU
		{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658}.Release|x64.Build.0 = Release|Any CPU
		{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658}.Release|x86.ActiveCfg = Release|Any CPU
		{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658}.Release|x86.Build.0 = Release|Any CPU
		{340F6024-40FF-46CF-BD07-16984D6E503B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{340F6024-40FF-46CF-BD07-16984D6E503B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{340F6024-40FF-46CF-BD07-16984D6E503B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{340F6024-40FF-46CF-BD07-16984D6E503B}.Debug|x64.Build.0 = Debug|Any CPU
		{340F6024-40FF-46CF-BD07-16984D6E503B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{340F6024-40FF-46CF-BD07-16984D6E503B}.Debug|x86.Build.0 = Debug|Any CPU
		{340F6024-40FF-46CF-BD07-16984D6E503B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{340F6024-40FF-46CF-BD07-16984D6E503B}.Release|Any CPU.Build.0 = Release|Any CPU
		{340F6024-40FF-46CF-BD07-16984D6E503B}.Release|x64.ActiveCfg = Release|Any CPU
		{340F6024-40FF-46CF-BD07-16984D6E503B}.Release|x64.Build.0 = Release|Any CPU
		{340F6024-40FF-46CF-BD07-16984D6E503B}.Release|x86.ActiveCfg = Release|Any CPU
		{340F6024-40FF-46CF-BD07-16984D6E503B}.Release|x86.Build.0 = Release|Any CPU
		{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E}.Debug|x64.Build.0 = Debug|Any CPU
		{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E}.Debug|x86.Build.0 = Debug|Any CPU
		{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E}.Release|Any CPU.Build.0 = Release|Any CPU
		{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E}.Release|x64.ActiveCfg = Release|Any CPU
		{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E}.Release|x64.Build.0 = Release|Any CPU
		{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E}.Release|x86.ActiveCfg = Release|Any CPU
		{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E}.Release|x86.Build.0 = Release|Any CPU
		{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303}.Debug|x64.ActiveCfg = Debug|Any CPU
		{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303}.Debug|x64.Build.0 = Debug|Any CPU
		{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303}.Debug|x86.ActiveCfg = Debug|Any CPU
		{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303}.Debug|x86.Build.0 = Debug|Any CPU
		{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303}.Release|Any CPU.Build.0 = Release|Any CPU
		{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303}.Release|x64.ActiveCfg = Release|Any CPU
		{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303}.Release|x64.Build.0 = Release|Any CPU
		{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303}.Release|x86.ActiveCfg = Release|Any CPU
		{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{E1EB3B85-D5C7-422B-9D75-156CE3EECFF4} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{A1D52FFF-BE93-4424-84DE-1F4A9284E306} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{3E0DACFC-2365-4CB6-BD7C-81CE4D8EF658} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{340F6024-40FF-46CF-BD07-16984D6E503B} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{D02DCD58-36F5-4D26-B068-16BC5EB6FC0E} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{0DFCBD66-1BEB-40AC-87E6-BF1A07CCE303} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
	EndGlobalSection
EndGlobal
