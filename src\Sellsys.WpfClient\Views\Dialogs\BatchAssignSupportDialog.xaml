<Window x:Class="Sellsys.WpfClient.Views.Dialogs.BatchAssignSupportDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="批量分配客服人员" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">

    <Window.Resources>
        <!-- 对话框样式 -->
        <Style x:Key="DialogTitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#303133"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style x:Key="DialogLabelStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#303133"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#909399"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Width" Value="80"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                BorderBrush="#7D7D7D"
                                BorderThickness="1">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#7D7D7D"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0" Text="批量分配客服人员" 
                   Style="{StaticResource DialogTitleStyle}" 
                   Margin="0,0,0,20"/>

        <!-- Customer List -->
        <GroupBox Grid.Row="1" Header="待分配客户" Margin="0,0,0,20">
            <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="200">
                <ItemsControl ItemsSource="{Binding SelectedCustomers}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" 
                                    Margin="0,2" Padding="10,5">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="{Binding Name}" 
                                               FontWeight="SemiBold" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="1" Text="{Binding FullAddress}" 
                                               Foreground="Gray" Margin="10,0" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="2" Text="{Binding IndustryType}" 
                                               Foreground="Blue" VerticalAlignment="Center"/>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </GroupBox>

        <!-- Support Person Selection -->
        <GroupBox Grid.Row="2" Header="选择客服人员" Margin="0,0,0,20">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Group Selection -->
                <Grid Grid.Row="0" Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="客服组:" VerticalAlignment="Center"/>
                    <ComboBox Grid.Column="1"
                              ItemsSource="{Binding Groups}"
                              SelectedItem="{Binding SelectedGroup}"
                              DisplayMemberPath="Name"
                              FontFamily="Microsoft YaHei UI"
                              FontSize="14"
                              Height="32"/>
                </Grid>

                <!-- Support Person Selection -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="客服人员:" VerticalAlignment="Center"/>
                    <ComboBox Grid.Column="1"
                              ItemsSource="{Binding SupportPersons}"
                              SelectedItem="{Binding SelectedSupportPerson}"
                              DisplayMemberPath="Name"
                              FontFamily="Microsoft YaHei UI"
                              FontSize="14"
                              Height="32"/>
                </Grid>
            </Grid>
        </GroupBox>

        <!-- Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="确定"
                    Command="{Binding AssignCommand}"
                    Background="#409EFF"
                    Foreground="White"
                    FontFamily="Microsoft YaHei UI"
                    FontSize="14"
                    Width="80" Height="32" Margin="0,0,10,0"
                    Cursor="Hand"/>
            <Button Content="取消"
                    Command="{Binding CancelCommand}"
                    Style="{StaticResource SecondaryButtonStyle}"
                    Width="80" Height="32"/>
        </StackPanel>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="4" Background="#80000000">
            <Grid.Style>
                <Style TargetType="Grid">
                    <Setter Property="Visibility" Value="Collapsed"/>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsLoading}" Value="True">
                            <Setter Property="Visibility" Value="Visible"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Grid.Style>
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="正在分配..." Foreground="White" HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
