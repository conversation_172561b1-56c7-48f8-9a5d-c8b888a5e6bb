using System;
using System.Windows;

namespace TestApp
{
    public partial class App : Application
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                Console.WriteLine("Starting test app...");
                var app = new App();
                app.InitializeComponent();
                app.Run();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack: {ex.StackTrace}");
                MessageBox.Show($"Error: {ex.Message}", "Test App Error");
            }
        }

        private void InitializeComponent()
        {
            this.StartupUri = new Uri("MainWindow.xaml", UriKind.Relative);
        }
    }

    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Title = "Test Window";
            this.Width = 800;
            this.Height = 600;
            this.WindowStartupLocation = WindowStartupLocation.CenterScreen;
        }
    }
}
