<Window x:Class="Sellsys.WpfClient.Views.Dialogs.AddEditProductDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:viewmodels="clr-namespace:Sellsys.WpfClient.ViewModels.Dialogs"
        d:DataContext="{d:DesignInstance Type=viewmodels:AddEditProductDialogViewModel}"
        mc:Ignorable="d"
        Title="{Binding DialogTitle}"
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Title Bar -->
            <RowDefinition Height="*"/>    <!-- Content -->
            <RowDefinition Height="Auto"/> <!-- Buttons -->
        </Grid.RowDefinitions>

        <!-- Title Bar -->
        <Border Grid.Row="0" 
                Background="{StaticResource PopupTitleBrush}" 
                Height="50">
            <TextBlock Text="{Binding DialogTitle}"
                       Style="{StaticResource TitleTextStyle}"
                       VerticalAlignment="Center"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" 
                      VerticalScrollBarVisibility="Auto"
                      Padding="30,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Product Name -->
                <TextBlock Grid.Row="0" Grid.Column="0"
                           Text="产品名称："
                           Style="{StaticResource GlobalTextStyle}"
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>
                <TextBox Grid.Row="0" Grid.Column="1"
                         Text="{Binding ProductName, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource StandardTextBoxStyle}"
                         Margin="0,0,0,15"/>

                <!-- Specification -->
                <TextBlock Grid.Row="1" Grid.Column="0"
                           Text="型号规格："
                           Style="{StaticResource GlobalTextStyle}"
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>
                <TextBox Grid.Row="1" Grid.Column="1"
                         Text="{Binding Specification, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource StandardTextBoxStyle}"
                         Margin="0,0,0,15"/>

                <!-- Unit -->
                <TextBlock Grid.Row="2" Grid.Column="0"
                           Text="计量单位："
                           Style="{StaticResource GlobalTextStyle}"
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>
                <ComboBox Grid.Row="2" Grid.Column="1"
                          ItemsSource="{Binding AvailableUnits}"
                          SelectedItem="{Binding SelectedUnit}"
                          IsEditable="True"
                          Text="{Binding Unit, UpdateSourceTrigger=PropertyChanged}"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Margin="0,0,0,15"/>

                <!-- List Price -->
                <TextBlock Grid.Row="3" Grid.Column="0"
                           Text="产品定价："
                           Style="{StaticResource GlobalTextStyle}"
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>
                <TextBox Grid.Row="3" Grid.Column="1"
                         Text="{Binding ListPrice, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource StandardTextBoxStyle}"
                         Margin="0,0,0,15"/>

                <!-- Min Price -->
                <TextBlock Grid.Row="4" Grid.Column="0"
                           Text="最低控价："
                           Style="{StaticResource GlobalTextStyle}"
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>
                <TextBox Grid.Row="4" Grid.Column="1"
                         Text="{Binding MinPrice, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource StandardTextBoxStyle}"
                         Margin="0,0,0,15"/>

                <!-- Sales Commission -->
                <TextBlock Grid.Row="5" Grid.Column="0"
                           Text="销售提成："
                           Style="{StaticResource GlobalTextStyle}"
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>
                <TextBox Grid.Row="5" Grid.Column="1"
                         Text="{Binding SalesCommission, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource StandardTextBoxStyle}"
                         Margin="0,0,0,15"/>

                <!-- Supervisor Commission -->
                <TextBlock Grid.Row="6" Grid.Column="0"
                           Text="主管提成："
                           Style="{StaticResource GlobalTextStyle}"
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>
                <TextBox Grid.Row="6" Grid.Column="1"
                         Text="{Binding SupervisorCommission, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource StandardTextBoxStyle}"
                         Margin="0,0,0,15"/>

                <!-- Manager Commission -->
                <TextBlock Grid.Row="7" Grid.Column="0"
                           Text="经理提成："
                           Style="{StaticResource GlobalTextStyle}"
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>
                <TextBox Grid.Row="7" Grid.Column="1"
                         Text="{Binding ManagerCommission, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource StandardTextBoxStyle}"
                         Margin="0,0,0,15"/>
            </Grid>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" 
                Background="{StaticResource HoverBackgroundBrush}"
                Padding="20,15">
            <StackPanel Orientation="Horizontal" 
                        HorizontalAlignment="Right">
                <Button Content="关闭"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource DeleteButtonStyle}"
                        Width="100"
                        Height="35"
                        Margin="0,0,15,0"/>
                <Button Content="保存"
                        Command="{Binding SaveCommand}"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Width="100"
                        Height="35"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
