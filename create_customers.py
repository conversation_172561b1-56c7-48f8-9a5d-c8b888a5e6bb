#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量创建客户数据脚本
"""

import requests
import json
import random
from datetime import datetime

# API配置
BASE_URL = "http://localhost:5078/api"
CURRENT_USER_ID = 1  # 使用张经理的ID

# 员工ID配置
SALES_PERSONS = [1, 2, 3]  # 张经理、李销售、王销售
SUPPORT_PERSONS = [4]      # 赵客服

# 客户数据模板
CUSTOMER_DATA = [
    {
        "name": "北京科技有限公司",
        "province": "北京市",
        "city": "海淀区",
        "address": "中关村软件园A座1001室",
        "remarks": "高新技术企业，主要从事软件开发",
        "industryTypes": "软件开发,信息技术",
        "contacts": [
            {"name": "张总", "phone": "13901234567", "isPrimary": True},
            {"name": "李经理", "phone": "13901234568", "isPrimary": False}
        ]
    },
    {
        "name": "上海贸易集团",
        "province": "上海市", 
        "city": "浦东新区",
        "address": "陆家嘴金融中心B座2001室",
        "remarks": "大型贸易公司，业务遍及全国",
        "industryTypes": "贸易,进出口",
        "contacts": [
            {"name": "王总", "phone": "13801234567", "isPrimary": True},
            {"name": "陈助理", "phone": "13801234568", "isPrimary": False}
        ]
    },
    {
        "name": "广州制造企业",
        "province": "广东省",
        "city": "广州市",
        "address": "天河区珠江新城C座1501室",
        "remarks": "专业制造商，产品质量优良",
        "industryTypes": "制造业,机械设备",
        "contacts": [
            {"name": "刘总", "phone": "13701234567", "isPrimary": True}
        ]
    },
    {
        "name": "深圳电子科技",
        "province": "广东省",
        "city": "深圳市", 
        "address": "南山区科技园D座801室",
        "remarks": "电子产品研发制造",
        "industryTypes": "电子,科技",
        "contacts": [
            {"name": "周总", "phone": "13601234567", "isPrimary": True},
            {"name": "吴工程师", "phone": "13601234568", "isPrimary": False}
        ]
    },
    {
        "name": "杭州互联网公司",
        "province": "浙江省",
        "city": "杭州市",
        "address": "西湖区文三路E座1201室",
        "remarks": "互联网平台运营商",
        "industryTypes": "互联网,电商",
        "contacts": [
            {"name": "马总", "phone": "13501234567", "isPrimary": True}
        ]
    },
    {
        "name": "南京医药有限公司",
        "province": "江苏省",
        "city": "南京市",
        "address": "建邺区河西新城F座901室",
        "remarks": "医药流通企业，覆盖华东地区",
        "industryTypes": "医药,健康",
        "contacts": [
            {"name": "孙总", "phone": "13401234567", "isPrimary": True},
            {"name": "钱经理", "phone": "13401234568", "isPrimary": False}
        ]
    },
    {
        "name": "成都餐饮连锁",
        "province": "四川省",
        "city": "成都市",
        "address": "锦江区春熙路G座601室",
        "remarks": "知名餐饮连锁品牌",
        "industryTypes": "餐饮,服务业",
        "contacts": [
            {"name": "赵总", "phone": "13301234567", "isPrimary": True}
        ]
    },
    {
        "name": "武汉物流集团",
        "province": "湖北省",
        "city": "武汉市",
        "address": "汉口区江汉路H座1101室",
        "remarks": "大型物流企业，仓储配送一体化",
        "industryTypes": "物流,运输",
        "contacts": [
            {"name": "冯总", "phone": "13201234567", "isPrimary": True},
            {"name": "卫经理", "phone": "13201234568", "isPrimary": False}
        ]
    },
    {
        "name": "西安建筑工程",
        "province": "陕西省",
        "city": "西安市",
        "address": "雁塔区高新区I座701室",
        "remarks": "建筑工程承包商",
        "industryTypes": "建筑,工程",
        "contacts": [
            {"name": "蒋总", "phone": "13101234567", "isPrimary": True}
        ]
    },
    {
        "name": "青岛海产品公司",
        "province": "山东省",
        "city": "青岛市",
        "address": "市南区海滨路J座501室",
        "remarks": "海产品加工出口企业",
        "industryTypes": "食品,海产品",
        "contacts": [
            {"name": "韩总", "phone": "13001234567", "isPrimary": True},
            {"name": "杨经理", "phone": "13001234568", "isPrimary": False}
        ]
    },
    {
        "name": "天津化工企业",
        "province": "天津市",
        "city": "滨海新区",
        "address": "开发区化工园K座301室",
        "remarks": "化工产品生产企业",
        "industryTypes": "化工,制造",
        "contacts": [
            {"name": "朱总", "phone": "12901234567", "isPrimary": True}
        ]
    },
    {
        "name": "重庆汽车配件",
        "province": "重庆市",
        "city": "渝北区",
        "address": "两路工业园L座201室",
        "remarks": "汽车零部件制造商",
        "industryTypes": "汽车,配件",
        "contacts": [
            {"name": "秦总", "phone": "12801234567", "isPrimary": True},
            {"name": "尤经理", "phone": "12801234568", "isPrimary": False}
        ]
    },
    {
        "name": "长沙教育科技",
        "province": "湖南省",
        "city": "长沙市",
        "address": "岳麓区大学城M座1001室",
        "remarks": "在线教育平台提供商",
        "industryTypes": "教育,科技",
        "contacts": [
            {"name": "许总", "phone": "12701234567", "isPrimary": True}
        ]
    },
    {
        "name": "昆明旅游开发",
        "province": "云南省",
        "city": "昆明市",
        "address": "五华区翠湖路N座801室",
        "remarks": "旅游景区开发运营",
        "industryTypes": "旅游,服务",
        "contacts": [
            {"name": "何总", "phone": "12601234567", "isPrimary": True},
            {"name": "吕经理", "phone": "12601234568", "isPrimary": False}
        ]
    },
    {
        "name": "福州纺织有限公司",
        "province": "福建省",
        "city": "福州市",
        "address": "鼓楼区五一路O座601室",
        "remarks": "纺织品生产出口企业",
        "industryTypes": "纺织,制造",
        "contacts": [
            {"name": "施总", "phone": "12501234567", "isPrimary": True}
        ]
    },
    {
        "name": "哈尔滨农业科技",
        "province": "黑龙江省",
        "city": "哈尔滨市",
        "address": "南岗区学府路P座401室",
        "remarks": "农业技术推广企业",
        "industryTypes": "农业,科技",
        "contacts": [
            {"name": "张总", "phone": "12401234567", "isPrimary": True},
            {"name": "孔经理", "phone": "12401234568", "isPrimary": False}
        ]
    },
    {
        "name": "沈阳机械制造",
        "province": "辽宁省",
        "city": "沈阳市",
        "address": "和平区中山路Q座301室",
        "remarks": "重型机械设备制造",
        "industryTypes": "机械,制造",
        "contacts": [
            {"name": "曹总", "phone": "12301234567", "isPrimary": True}
        ]
    },
    {
        "name": "石家庄医疗器械",
        "province": "河北省",
        "city": "石家庄市",
        "address": "长安区建设路R座501室",
        "remarks": "医疗设备研发生产",
        "industryTypes": "医疗,器械",
        "contacts": [
            {"name": "严总", "phone": "12201234567", "isPrimary": True},
            {"name": "华经理", "phone": "12201234568", "isPrimary": False}
        ]
    },
    {
        "name": "太原能源集团",
        "province": "山西省",
        "city": "太原市",
        "address": "迎泽区迎泽大街S座701室",
        "remarks": "新能源开发利用企业",
        "industryTypes": "能源,环保",
        "contacts": [
            {"name": "金总", "phone": "12101234567", "isPrimary": True}
        ]
    },
    {
        "name": "兰州环保科技",
        "province": "甘肃省",
        "city": "兰州市",
        "address": "城关区南昌路T座401室",
        "remarks": "环保设备制造销售",
        "industryTypes": "环保,科技",
        "contacts": [
            {"name": "魏总", "phone": "12001234567", "isPrimary": True},
            {"name": "陶经理", "phone": "12001234568", "isPrimary": False}
        ]
    }
]

def create_customer(customer_data):
    """创建单个客户"""
    # 随机分配销售人员和客服人员
    sales_person_id = random.choice(SALES_PERSONS)
    support_person_id = random.choice(SUPPORT_PERSONS)
    
    # 构建请求数据
    request_data = {
        "name": customer_data["name"],
        "province": customer_data["province"],
        "city": customer_data["city"],
        "address": customer_data["address"],
        "remarks": customer_data["remarks"],
        "industryTypes": customer_data["industryTypes"],
        "salesPersonId": sales_person_id,
        "supportPersonId": support_person_id,
        "contacts": customer_data["contacts"]
    }
    
    # 发送POST请求
    url = f"{BASE_URL}/customers?currentUserId={CURRENT_USER_ID}"
    headers = {"Content-Type": "application/json"}
    
    try:
        response = requests.post(url, json=request_data, headers=headers)
        if response.status_code in [200, 201]:
            result = response.json()
            if result.get("isSuccess"):
                print(f"✓ 成功创建客户: {customer_data['name']}")
                return True
            else:
                print(f"✗ 创建客户失败: {customer_data['name']} - {result.get('message', '未知错误')}")
                return False
        else:
            print(f"✗ 创建客户失败: {customer_data['name']} - HTTP {response.status_code}")
            print(f"  响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"✗ 创建客户异常: {customer_data['name']} - {str(e)}")
        return False

def main():
    """主函数"""
    print("开始批量创建客户数据...")
    print(f"目标创建数量: {len(CUSTOMER_DATA)} 个客户")
    print("-" * 50)
    
    success_count = 0
    fail_count = 0
    
    for i, customer_data in enumerate(CUSTOMER_DATA, 1):
        print(f"[{i}/{len(CUSTOMER_DATA)}] 正在创建: {customer_data['name']}")
        if create_customer(customer_data):
            success_count += 1
        else:
            fail_count += 1
        print()
    
    print("-" * 50)
    print(f"批量创建完成!")
    print(f"成功: {success_count} 个")
    print(f"失败: {fail_count} 个")
    print(f"总计: {success_count + fail_count} 个")

if __name__ == "__main__":
    main()
