<UserControl x:Class="Sellsys.WpfClient.Views.SystemSettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Sellsys.WpfClient.Views"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Top Navigation -->
            <RowDefinition Height="*"/>    <!-- Content -->
        </Grid.RowDefinitions>

        <!-- Top Navigation Bar -->
        <Border Grid.Row="0" Background="#F8F9FA" Height="50">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Top" Margin="15,7,20,0">
                <Button Content="部门管理"
                        Command="{Binding ShowDepartmentManagementCommand}"
                        Style="{StaticResource NavigationButtonStyle}"
                        Background="{Binding IsDepartmentTabActive, Converter={StaticResource BoolToActiveBackgroundConverter}}"
                        Foreground="White"
                        Width="100" Height="35" Margin="0,0,10,0"/>
                <Button Content="部门分组"
                        Command="{Binding ShowDepartmentGroupCommand}"
                        Style="{StaticResource NavigationButtonStyle}"
                        Background="{Binding IsDepartmentGroupTabActive, Converter={StaticResource BoolToActiveBackgroundConverter}}"
                        Foreground="White"
                        Width="100" Height="35" Margin="0,0,10,0"/>
                <Button Content="员工管理"
                        Command="{Binding ShowEmployeeManagementCommand}"
                        Style="{StaticResource NavigationButtonStyle}"
                        Background="{Binding IsEmployeeTabActive, Converter={StaticResource BoolToActiveBackgroundConverter}}"
                        Foreground="White"
                        Width="100" Height="35" Margin="0,0,10,0"/>
                <Button Content="用户权限"
                        Command="{Binding ShowUserPermissionCommand}"
                        Style="{StaticResource NavigationButtonStyle}"
                        Background="{Binding IsPermissionTabActive, Converter={StaticResource BoolToActiveBackgroundConverter}}"
                        Foreground="White"
                        Width="100" Height="35" Margin="0,0,10,0"/>
            </StackPanel>
        </Border>

        <!-- Content Area -->
        <Grid Grid.Row="1" Margin="0">
            <!-- Department Management Tab -->
            <Grid Visibility="{Binding IsDepartmentTabActive, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/> <!-- Action Buttons -->
                    <RowDefinition Height="*"/>    <!-- Data Grid -->
                </Grid.RowDefinitions>

                <!-- Action Buttons -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="15,0,0,15">
                    <Button Content="添加部门"
                            Command="{Binding AddDepartmentCommand}"
                            Style="{StaticResource PrimaryButtonStyle}"
                            Width="100" Height="35" Margin="0,0,10,0"
                            Visibility="{Binding ., Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}"/>
                </StackPanel>

                <!-- Departments Data Grid -->
                <Border Grid.Row="1" Background="White" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,0,0" CornerRadius="0">
                    <DataGrid ItemsSource="{Binding Departments}"
                              SelectedItem="{Binding SelectedDepartment}"
                              Style="{StaticResource StandardDataGridStyle}"
                              ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                              RowStyle="{StaticResource DataGridRowStyle}"
                              RowHeaderWidth="0">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="序号"
                                                Binding="{Binding Id}"
                                                Width="80" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="部门名称"
                                                Binding="{Binding Name}"
                                                Width="300" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="创建时间"
                                                Binding="{Binding CreatedAt, StringFormat=yyyy-MM-dd HH:mm:ss}"
                                                Width="200" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="操作" Width="150"
                                                    Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="编辑"
                                                    Command="{Binding DataContext.EditDepartmentCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    Style="{StaticResource EditButtonStyle}"
                                                    Width="50" Height="25" Margin="0,0,5,0"
                                                    Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}"/>
                                            <Button Content="删除"
                                                    Command="{Binding DataContext.DeleteDepartmentCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    Style="{StaticResource DeleteButtonStyle}"
                                                    Width="50" Height="25"
                                                    Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Border>
            </Grid>

            <!-- Department Group Tab -->
            <Grid Visibility="{Binding IsDepartmentGroupTabActive, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/> <!-- Action Buttons -->
                    <RowDefinition Height="*"/>    <!-- Data Grid -->
                </Grid.RowDefinitions>

                <!-- Action Buttons -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="15,0,0,15">
                    <Button Content="添加分组"
                            Command="{Binding AddDepartmentGroupCommand}"
                            Style="{StaticResource PrimaryButtonStyle}"
                            Width="100" Height="35" Margin="0,0,10,0"
                            Visibility="{Binding ., Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}"/>
                </StackPanel>

                <!-- Department Groups Data Grid -->
                <Border Grid.Row="1" Background="White" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,0,0" CornerRadius="0">
                    <DataGrid ItemsSource="{Binding DepartmentGroups}"
                              SelectedItem="{Binding SelectedDepartmentGroup}"
                              Style="{StaticResource StandardDataGridStyle}"
                              ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                              RowStyle="{StaticResource DataGridRowStyle}"
                              RowHeaderWidth="0">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="序号"
                                                Binding="{Binding Id}"
                                                Width="80" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="部门名称"
                                                Binding="{Binding Department.Name}"
                                                Width="200" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="部门分组"
                                                Binding="{Binding Name}"
                                                Width="200" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="创建时间"
                                                Binding="{Binding CreatedAt, StringFormat=yyyy-MM-dd HH:mm:ss}"
                                                Width="200" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="操作" Width="150"
                                                    Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="编辑"
                                                    Command="{Binding DataContext.EditDepartmentGroupCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    Style="{StaticResource EditButtonStyle}"
                                                    Width="50" Height="25" Margin="0,0,5,0"
                                                    Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}"/>
                                            <Button Content="删除"
                                                    Command="{Binding DataContext.DeleteDepartmentGroupCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    Style="{StaticResource DeleteButtonStyle}"
                                                    Width="50" Height="25"
                                                    Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Border>
            </Grid>

            <!-- Employee Management Tab -->
            <Grid Visibility="{Binding IsEmployeeTabActive, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/> <!-- Action Buttons -->
                    <RowDefinition Height="*"/>    <!-- Data Grid -->
                </Grid.RowDefinitions>

                <!-- Action Buttons -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="15,0,0,15">
                    <Button Content="添加员工"
                            Command="{Binding AddEmployeeCommand}"
                            Style="{StaticResource PrimaryButtonStyle}"
                            Width="100" Height="35" Margin="0,0,10,0"
                            Visibility="{Binding ., Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}"/>
                </StackPanel>

                <!-- Employees Data Grid -->
                <Border Grid.Row="1" Background="White" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,0,0" CornerRadius="0">
                    <DataGrid ItemsSource="{Binding Employees}"
                              SelectedItem="{Binding SelectedEmployee}"
                              Style="{StaticResource StandardDataGridStyle}"
                              ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                              RowStyle="{StaticResource DataGridRowStyle}"
                              RowHeaderWidth="0">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="序号"
                                                Binding="{Binding Id}"
                                                Width="60" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="部门名称"
                                                Binding="{Binding DepartmentName}"
                                                Width="100" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="部门分组"
                                                Binding="{Binding GroupName}"
                                                Width="100" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="员工姓名"
                                                Binding="{Binding Name}"
                                                Width="100" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="岗位职务"
                                                Binding="{Binding RoleName}"
                                                Width="100" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="登录账号"
                                                Binding="{Binding LoginUsername}"
                                                Width="100" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="创建时间"
                                                Binding="{Binding CreatedAt, StringFormat=yyyy-MM-dd HH:mm:ss}"
                                                Width="150" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="操作" Width="150"
                                                    Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="编辑"
                                                    Command="{Binding DataContext.EditEmployeeCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    Style="{StaticResource EditButtonStyle}"
                                                    Width="50" Height="25" Margin="0,0,5,0"
                                                    Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}"/>
                                            <Button Content="删除"
                                                    Command="{Binding DataContext.DeleteEmployeeCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    Style="{StaticResource DeleteButtonStyle}"
                                                    Width="50" Height="25"
                                                    Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Border>
            </Grid>

            <!-- User Permission Tab -->
            <Grid Visibility="{Binding IsPermissionTabActive, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/> <!-- Action Buttons -->
                    <RowDefinition Height="*"/>    <!-- Data Grid -->
                </Grid.RowDefinitions>

                <!-- Action Buttons -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="15,0,0,15">
                    <Button Content="添加权限"
                            Command="{Binding AddPermissionCommand}"
                            Style="{StaticResource PrimaryButtonStyle}"
                            Width="100" Height="35" Margin="0,0,10,0"
                            Visibility="{Binding ., Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}"/>
                </StackPanel>

                <!-- Permissions Data Grid -->
                <Border Grid.Row="1" Background="White" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,0,0" CornerRadius="0">
                    <DataGrid ItemsSource="{Binding Roles}"
                              SelectedItem="{Binding SelectedRole}"
                              Style="{StaticResource StandardDataGridStyle}"
                              ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                              RowStyle="{StaticResource DataGridRowStyle}"
                              RowHeaderWidth="0">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="序号"
                                                Binding="{Binding Id}"
                                                Width="80" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="部门名称"
                                                Binding="{Binding DepartmentName}"
                                                Width="200" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="岗位职务"
                                                Binding="{Binding Name}"
                                                Width="200" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="创建时间"
                                                Binding="{Binding CreatedAt, StringFormat=yyyy-MM-dd HH:mm:ss}"
                                                Width="200" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="操作" Width="150"
                                                    Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="编辑"
                                                    Command="{Binding DataContext.EditPermissionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    Style="{StaticResource EditButtonStyle}"
                                                    Width="50" Height="25" Margin="0,0,5,0"
                                                    Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}"/>
                                            <Button Content="删除"
                                                    Command="{Binding DataContext.DeletePermissionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    Style="{StaticResource DeleteButtonStyle}"
                                                    Width="50" Height="25"
                                                    Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=系统设置}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Border>
            </Grid>
        </Grid>
    </Grid>
</UserControl>
