<UserControl x:Class="Sellsys.WpfClient.Views.FinanceManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Sellsys.WpfClient.Views"
             xmlns:controls="clr-namespace:Sellsys.WpfClient.Controls"
             xmlns:converters="clr-namespace:Sellsys.WpfClient.Converters"
             mc:Ignorable="d"
             d:DesignHeight="800" d:DesignWidth="1600">

    <!-- 定义资源 -->
    <UserControl.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>

        <!-- 蓝色主题样式 -->
        <SolidColorBrush x:Key="PrimaryBlueBrush" Color="#409EFF"/>
        <SolidColorBrush x:Key="LightBlueBrush" Color="#ECF5FF"/>
        <SolidColorBrush x:Key="BorderBrush" Color="#DCDFE6"/>
        <SolidColorBrush x:Key="TextBrush" Color="#303133"/>
        <SolidColorBrush x:Key="SecondaryTextBrush" Color="#606266"/>
        <SolidColorBrush x:Key="TableHeaderBrush" Color="#F5F7FA"/>
        <SolidColorBrush x:Key="TableRowEvenBrush" Color="#FAFAFA"/>

        <!-- 按钮样式 -->
        <Style x:Key="BlueButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBlueBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#66B1FF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3A8EE6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 灰色按钮样式 -->
        <Style x:Key="GrayButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#909399"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#A6A9AD"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#82848A"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- 筛选区域 -->
            <RowDefinition Height="*"/>    <!-- 数据表格 -->
            <RowDefinition Height="Auto"/> <!-- 分页栏 -->
        </Grid.RowDefinitions>

        <!-- 筛选区域 -->
        <Border Grid.Row="0" Style="{StaticResource SearchAreaBorderStyle}">
            <StackPanel Orientation="Horizontal">
                <!-- 输入客户名称 (输入框) -->
                <TextBlock Text="输入客户名称:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBox Text="{Binding SearchCustomerName, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource StandardTextBoxStyle}"
                         Width="120"
                         Height="32"
                         Margin="0,0,15,0"/>

                <!-- 产品名称 (下拉框) -->
                <TextBlock Text="产品名称:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox ItemsSource="{Binding FilterOptions.Products}"
                          SelectedItem="{Binding SelectedProduct}"
                          DisplayMemberPath="Text"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="100"
                          Height="32"
                          Margin="0,0,15,0"/>

                <!-- 生效日期 (下拉框) -->
                <TextBlock Text="生效日期:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox ItemsSource="{Binding EffectiveDateOptions}"
                          SelectedItem="{Binding SelectedEffectiveDate}"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="100"
                          Height="32"
                          Margin="0,0,15,0"/>

                <!-- 到期日期 (下拉框) -->
                <TextBlock Text="到期日期:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox ItemsSource="{Binding ExpiryDateOptions}"
                          SelectedItem="{Binding SelectedExpiryDate}"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="100"
                          Height="32"
                          Margin="0,0,15,0"/>

                <!-- 签单日期 (下拉框) -->
                <TextBlock Text="签单日期:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox ItemsSource="{Binding CreatedDateOptions}"
                          SelectedItem="{Binding SelectedCreatedDate}"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="100"
                          Height="32"
                          Margin="0,0,15,0"/>

                <!-- 订单状态 (下拉框) -->
                <TextBlock Text="订单状态:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox ItemsSource="{Binding FilterOptions.OrderStatuses}"
                          SelectedItem="{Binding SelectedOrderStatus}"
                          DisplayMemberPath="Text"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="100"
                          Height="32"
                          Margin="0,0,15,0"/>

                <!-- 销售人员 (下拉框) -->
                <TextBlock Text="销售人员:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox ItemsSource="{Binding FilterOptions.SalesPersons}"
                          SelectedItem="{Binding SelectedSalesPerson}"
                          DisplayMemberPath="Text"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="100"
                          Height="32"
                          Margin="0,0,15,0"/>

                <!-- 查询和重置按钮 -->
                <Button Content="查询"
                        Command="{Binding SearchCommand}"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Width="60"
                        Height="32"
                        Margin="0,0,10,0"/>
                <Button Content="重置"
                        Command="{Binding ResetFiltersCommand}"
                        Style="{StaticResource GrayButtonStyle}"
                        Width="60"
                        Height="32"
                        Margin="0"/>
            </StackPanel>
        </Border>

        <!-- 数据表格区域 -->
        <Border Grid.Row="1"
                Background="White"
                BorderBrush="{StaticResource BorderBrush}"
                BorderThickness="0,0,0,0"
                CornerRadius="0"
                Padding="0"
                Margin="0">
                    <Grid Margin="0" Background="White">
                        <!-- 加载指示器 -->
                        <Grid Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                              Background="#80FFFFFF">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <ProgressBar IsIndeterminate="True" Width="200" Height="20" Margin="0,0,0,10"/>
                                <TextBlock Text="正在加载..."
                                           FontSize="14"
                                           Foreground="{StaticResource TextBrush}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>

                        <!-- 财务订单明细表格 -->
                        <DataGrid x:Name="FinanceOrderDetailsDataGrid"
                                  ItemsSource="{Binding FinanceOrderDetails}"
                                  SelectedItem="{Binding SelectedOrderDetail}"
                                  Style="{StaticResource BlueHeaderDataGridStyle}"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  CanUserReorderColumns="False"
                                  CanUserResizeRows="False"
                                  CanUserSortColumns="True"
                                  IsReadOnly="True"
                                  SelectionMode="Single"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                  ScrollViewer.VerticalScrollBarVisibility="Disabled"
                                  MaxHeight="500"
                                  Margin="0"
                                  Padding="0"
                                  HorizontalAlignment="Stretch"
                                  VerticalAlignment="Top"
                                  CanUserResizeColumns="True">


                    <!-- 列头样式 -->
                    <DataGrid.ColumnHeaderStyle>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#007ACC"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Setter Property="BorderBrush" Value="#005A9B"/>
                            <Setter Property="BorderThickness" Value="0,0,1,1"/>
                            <Setter Property="Padding" Value="12,8"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                            <Setter Property="FontSize" Value="14"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                        </Style>
                    </DataGrid.ColumnHeaderStyle>

                    <!-- 行样式 -->
                    <DataGrid.RowStyle>
                        <Style TargetType="DataGridRow">
                            <Setter Property="Background" Value="White"/>
                            <Setter Property="BorderBrush" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="MinHeight" Value="60"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="{StaticResource LightBlueBrush}"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#E3F2FD"/>
                                    <Setter Property="Foreground" Value="#303133"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.RowStyle>

                    <!-- 单元格样式 -->
                    <DataGrid.CellStyle>
                        <Style TargetType="DataGridCell">
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
                            <Setter Property="Padding" Value="8,8"/>
                            <Setter Property="VerticalAlignment" Value="Stretch"/>
                            <Setter Property="VerticalContentAlignment" Value="Top"/>
                        </Style>
                    </DataGrid.CellStyle>

                    <DataGrid.Columns>
                        <!-- 序号 -->
                        <DataGridTextColumn Header="序号"
                                            Width="60" MinWidth="60"
                                            IsReadOnly="True">
                            <DataGridTextColumn.Binding>
                                <Binding RelativeSource="{RelativeSource AncestorType=DataGridRow}"
                                         Converter="{StaticResource RowIndexConverter}"/>
                            </DataGridTextColumn.Binding>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 客户单位 -->
                        <DataGridTextColumn Header="客户单位"
                                            Binding="{Binding CustomerName}"
                                            Width="240" MinWidth="180"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Left"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="Margin" Value="8,8,8,8"/>
                                    <Setter Property="LineHeight" Value="22"/>
                                    <Setter Property="TextTrimming" Value="None"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 产品名称 -->
                        <DataGridTextColumn Header="产品名称"
                                            Binding="{Binding ProductName}"
                                            Width="180" MinWidth="160"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Left"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="Margin" Value="8,8,8,8"/>
                                    <Setter Property="LineHeight" Value="22"/>
                                    <Setter Property="TextTrimming" Value="None"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 型号规格 -->
                        <DataGridTextColumn Header="型号规格"
                                            Binding="{Binding ProductSpecification}"
                                            Width="140" MinWidth="120"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                    <Setter Property="ToolTip" Value="{Binding ProductSpecification}"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 产品定价 -->
                        <DataGridTextColumn Header="产品定价"
                                            Binding="{Binding FormattedListPrice}"
                                            Width="80" MinWidth="70"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 实际单价 -->
                        <DataGridTextColumn Header="实际单价"
                                            Binding="{Binding FormattedUnitPrice}"
                                            Width="80" MinWidth="70"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 数量 -->
                        <DataGridTextColumn Header="数量"
                                            Binding="{Binding Quantity}"
                                            Width="70" MinWidth="60"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 单位 -->
                        <DataGridTextColumn Header="单位"
                                            Binding="{Binding ProductUnit}"
                                            Width="70" MinWidth="60"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 订单金额 -->
                        <DataGridTextColumn Header="订单金额"
                                            Binding="{Binding FormattedTotalAmount}"
                                            Width="140" MinWidth="120"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 销售提成 -->
                        <DataGridTextColumn Header="销售提成"
                                            Binding="{Binding FormattedSalesCommission}"
                                            Width="90" MinWidth="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 主管提成 -->
                        <DataGridTextColumn Header="主管提成"
                                            Binding="{Binding FormattedSupervisorCommission}"
                                            Width="90" MinWidth="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 经理提成 -->
                        <DataGridTextColumn Header="经理提成"
                                            Binding="{Binding FormattedManagerCommission}"
                                            Width="100" MinWidth="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 签单日期 -->
                        <DataGridTextColumn Header="签单日期"
                                            Binding="{Binding FormattedCreatedDate}"
                                            Width="90" MinWidth="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 销售人 -->
                        <DataGridTextColumn Header="销售人"
                                            Binding="{Binding SalesPersonName}"
                                            Width="80" MinWidth="70"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 订单状态 -->
                        <DataGridTextColumn Header="订单状态"
                                            Binding="{Binding OrderStatus}"
                                            Width="80" MinWidth="70"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 到期日期 -->
                        <DataGridTextColumn Header="到期日期"
                                            Binding="{Binding FormattedExpiryDate}"
                                            Width="90" MinWidth="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 操作 -->
                        <DataGridTemplateColumn Header="操作" Width="120" MinWidth="120"
                                                Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=财务管理}">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="8,0,8,0">
                                        <!-- 确认收款按钮 - 仅在待收款状态显示 -->
                                        <Button Content="确认收款"
                                                Command="{Binding DataContext.ConfirmPaymentCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource BlueButtonStyle}"
                                                Width="85"
                                                Height="30"
                                                FontSize="12"
                                                Margin="0"
                                                Visibility="{Binding OrderStatus, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=待收款}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                    </DataGrid>
                    </Grid>
                </Border>

        <!-- Pagination Control -->
        <Border Grid.Row="2"
                Background="White"
                BorderBrush="{StaticResource BorderBrush}"
                BorderThickness="0,0,0,0"
                Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧分页控件 -->
                <controls:PaginationControl Grid.Column="0" DataContext="{Binding}" HorizontalAlignment="Left"/>

                <!-- 中间合计信息 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="合计:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding Summary.FormattedTotalAmount}" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    <TextBlock Text="销售提成:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding Summary.FormattedTotalSalesCommission}" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    <TextBlock Text="主管提成:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding Summary.FormattedTotalManagerCommission}" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    <TextBlock Text="经理提成:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding Summary.FormattedTotalDirectorCommission}" FontWeight="Bold" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>


                </Border>
    </Grid>
</UserControl>