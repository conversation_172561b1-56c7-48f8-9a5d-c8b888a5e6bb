﻿<Application x:Class="Sellsys.WpfClient.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:Sellsys.WpfClient"
             xmlns:views="clr-namespace:Sellsys.WpfClient.Views"
             xmlns:viewmodels="clr-namespace:Sellsys.WpfClient.ViewModels"
             xmlns:converters="clr-namespace:Sellsys.WpfClient.Converters"
             Startup="Application_Startup">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/GlobalStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <converters:NavigationButtonStyleConverter x:Key="NavigationButtonStyleConverter"/>
            <converters:NotNullToVisibleConverter x:Key="NotNullToVisibleConverter"/>
            <converters:RowIndexConverter x:Key="RowIndexConverter"/>
            <converters:OrderItemsToProductNamesConverter x:Key="OrderItemsToProductNamesConverter"/>
            <converters:OrderItemsToSpecificationsConverter x:Key="OrderItemsToSpecificationsConverter"/>
            <converters:OrderItemsToProductPricesConverter x:Key="OrderItemsToProductPricesConverter"/>
            <converters:OrderItemsToActualPricesConverter x:Key="OrderItemsToActualPricesConverter"/>
            <converters:OrderItemsToUnitsConverter x:Key="OrderItemsToUnitsConverter"/>
            <converters:BoolToActiveBackgroundConverter x:Key="BoolToActiveBackgroundConverter"/>
            <converters:BoolToActiveForegroundConverter x:Key="BoolToActiveForegroundConverter"/>
            <converters:StringToBooleanConverter x:Key="StringToBooleanConverter"/>
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
            <converters:PermissionToVisibilityConverter x:Key="PermissionToVisibilityConverter"/>
            <converters:PermissionToBooleanConverter x:Key="PermissionToBooleanConverter"/>
            <converters:AssignPermissionToVisibilityConverter x:Key="AssignPermissionToVisibilityConverter"/>
            <converters:OperationPermissionToVisibilityConverter x:Key="OperationPermissionToVisibilityConverter"/>
            <converters:OperationPermissionToBooleanConverter x:Key="OperationPermissionToBooleanConverter"/>
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

            <!-- ViewModel to View mappings -->
            <DataTemplate DataType="{x:Type viewmodels:ProductManagementViewModel}">
                <views:ProductManagementView/>
            </DataTemplate>
            <DataTemplate DataType="{x:Type viewmodels:CustomerManagementViewModel}">
                <views:CustomerManagementView/>
            </DataTemplate>
            <DataTemplate DataType="{x:Type viewmodels:SalesManagementViewModel}">
                <views:SalesManagementView/>
            </DataTemplate>
            <DataTemplate DataType="{x:Type viewmodels:OrderManagementViewModel}">
                <views:OrderManagementView/>
            </DataTemplate>
            <DataTemplate DataType="{x:Type viewmodels:AfterSalesViewModel}">
                <views:AfterSalesView/>
            </DataTemplate>
            <DataTemplate DataType="{x:Type viewmodels:FinanceManagementViewModel}">
                <views:FinanceManagementView/>
            </DataTemplate>
            <DataTemplate DataType="{x:Type viewmodels:SystemSettingsViewModel}">
                <views:SystemSettingsView/>
            </DataTemplate>

        </ResourceDictionary>
    </Application.Resources>
</Application>
