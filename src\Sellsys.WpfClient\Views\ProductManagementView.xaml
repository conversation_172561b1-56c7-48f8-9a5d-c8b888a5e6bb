<UserControl x:Class="Sellsys.WpfClient.Views.ProductManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:viewmodels="clr-namespace:Sellsys.WpfClient.ViewModels"
             xmlns:controls="clr-namespace:Sellsys.WpfClient.Controls"
             d:DataContext="{d:DesignInstance Type=viewmodels:ProductManagementViewModel}"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Action Button -->
            <RowDefinition Height="*"/>    <!-- Data Grid -->
            <RowDefinition Height="Auto"/> <!-- Pagination -->
        </Grid.RowDefinitions>

        <!-- Action Button Section -->
        <Border Grid.Row="0" Style="{StaticResource SearchAreaBorderStyle}" Padding="15,7,15,15">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Top">
                <Button Content="添加产品"
                        Command="{Binding AddProductCommand}"
                        Style="{StaticResource ActionButtonStyle}"
                        Visibility="{Binding ., Converter={StaticResource OperationPermissionToVisibilityConverter}, ConverterParameter=产品管理:Create}"/>
            </StackPanel>
        </Border>

        <!-- Data Grid -->
        <Border Grid.Row="1"
                Background="White"
                BorderBrush="{StaticResource BorderBrush}"
                BorderThickness="0,0,0,0">
            <Grid Margin="0" Background="White">
                <!-- Loading Indicator -->
                <Grid Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Grid.Background>
                        <SolidColorBrush Color="White" Opacity="0.7"/>
                    </Grid.Background>
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar IsIndeterminate="True" Width="200" Height="20" Margin="0,0,0,10"/>
                        <TextBlock Text="正在加载..."
                                   Style="{StaticResource GlobalTextStyle}"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>

                <!-- Products Data Grid -->
                <DataGrid x:Name="ProductsDataGrid"
                          ItemsSource="{Binding Products}"
                          SelectedItem="{Binding SelectedProduct}"
                          Style="{StaticResource ResponsiveBlueHeaderDataGridStyle}"
                          BorderThickness="0">
                    <DataGrid.Columns>
                        <!-- 序号 -->
                        <DataGridTextColumn Header="序号"
                                            Width="60" MinWidth="60"
                                            IsReadOnly="True">
                            <DataGridTextColumn.Binding>
                                <Binding RelativeSource="{RelativeSource AncestorType=DataGridRow}"
                                         Converter="{StaticResource RowIndexConverter}"/>
                            </DataGridTextColumn.Binding>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 产品名称 -->
                        <DataGridTextColumn Header="产品名称"
                                            Binding="{Binding Name}"
                                            Width="3*" MinWidth="120"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Left"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 型号规格 -->
                        <DataGridTextColumn Header="型号规格"
                                            Binding="{Binding SpecificationDisplay}"
                                            Width="2*" MinWidth="100"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Left"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 计量单位 -->
                        <DataGridTextColumn Header="计量单位"
                                            Binding="{Binding UnitDisplay}"
                                            Width="*" MinWidth="60"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 产品定价 -->
                        <DataGridTextColumn Header="产品定价"
                                            Binding="{Binding FormattedListPrice}"
                                            Width="*" MinWidth="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 最低控价 -->
                        <DataGridTextColumn Header="最低控价"
                                            Binding="{Binding FormattedMinPrice}"
                                            Width="*" MinWidth="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 销售提成 -->
                        <DataGridTextColumn Header="销售提成"
                                            Binding="{Binding FormattedSalesCommission}"
                                            Width="*" MinWidth="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 主管提成 -->
                        <DataGridTextColumn Header="主管提成"
                                            Binding="{Binding FormattedSupervisorCommission}"
                                            Width="*" MinWidth="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 经理提成 -->
                        <DataGridTextColumn Header="经理提成"
                                            Binding="{Binding FormattedManagerCommission}"
                                            Width="*" MinWidth="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 操作 -->
                        <DataGridTemplateColumn Header="操作" Width="120" MinWidth="120"
                                                Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=产品管理}">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="编辑"
                                                Command="{Binding DataContext.EditProductRowCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource BlueButtonStyle}"
                                                Width="50"
                                                Height="25"
                                                Margin="2"
                                                Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource OperationPermissionToVisibilityConverter}, ConverterParameter=产品管理:Edit}"/>
                                        <Button Content="删除"
                                                Command="{Binding DataContext.DeleteProductRowCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource DeleteButtonStyle}"
                                                Width="50"
                                                Height="25"
                                                Margin="2"
                                                Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource OperationPermissionToVisibilityConverter}, ConverterParameter=产品管理:Delete}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Pagination Control -->
        <Border Grid.Row="2"
                Background="White"
                BorderBrush="{StaticResource BorderBrush}"
                BorderThickness="0,0,0,0"
                Padding="10">
            <controls:PaginationControl DataContext="{Binding}"/>
        </Border>
    </Grid>
</UserControl>