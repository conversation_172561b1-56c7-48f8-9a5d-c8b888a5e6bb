using System.Collections.ObjectModel;
using System.ComponentModel;

namespace Sellsys.WpfClient.Models
{
    public class Customer : INotifyPropertyChanged
    {
        private bool _isSelected;

        public Customer()
        {
            // 初始化时订阅集合变化事件
            _contacts.CollectionChanged += OnContactsChanged;
        }
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Province { get; set; }
        public string? City { get; set; }
        public string? Address { get; set; }
        public string? Remarks { get; set; }
        public string? IndustryTypes { get; set; }
        public int? SalesPersonId { get; set; }
        public string? SalesPersonName { get; set; }
        public int? SupportPersonId { get; set; }
        public string? SupportPersonName { get; set; }
        public string? Status { get; set; } = "待跟进"; // 客户状态
        public DateTime CreatedAt { get; set; }

        private ObservableCollection<Contact> _contacts = new ObservableCollection<Contact>();
        public ObservableCollection<Contact> Contacts
        {
            get => _contacts;
            set
            {
                if (_contacts != value)
                {
                    // 取消订阅旧集合的事件
                    if (_contacts != null)
                    {
                        _contacts.CollectionChanged -= OnContactsChanged;
                    }

                    _contacts = value;

                    // 订阅新集合的事件
                    if (_contacts != null)
                    {
                        _contacts.CollectionChanged += OnContactsChanged;
                    }

                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(Contacts)));
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(ContactCount)));
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(PrimaryContact)));
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(PrimaryContactName)));
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(PrimaryContactPhone)));
                }
            }
        }

        // 显示用的完整地址
        public string FullAddress => $"{Province}{City}{Address}".Trim();
        
        // 主要联系人
        public Contact? PrimaryContact => Contacts.FirstOrDefault(c => c.IsPrimary);
        
        // 主要联系人姓名
        public string PrimaryContactName => PrimaryContact?.Name ?? "无";
        
        // 主要联系人电话
        public string PrimaryContactPhone => PrimaryContact?.Phone ?? "无";

        // 联系人数量
        public int ContactCount => Contacts?.Count ?? 0;

        // 客户意向
        public string CustomerIntention { get; set; } = "待分配";

        // 客户备注（显示用属性，返回实际备注内容）
        public string CustomerRemarks => string.IsNullOrWhiteSpace(Remarks) ? "无要求" : Remarks;

        // 客户状态
        public string CustomerStatus { get; set; } = "待联系";

        // 待办事项
        public string PendingTasks { get; set; } = "无";

        // 下次联系日期
        public DateTime? NextContactDate { get; set; }

        // 联系记录数量（销售跟进记录）
        private int _contactRecordCount = 0;
        public int ContactRecordCount
        {
            get => _contactRecordCount;
            set
            {
                if (_contactRecordCount != value)
                {
                    _contactRecordCount = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(ContactRecordCount)));
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(ContactRecordCountDisplay)));
                }
            }
        }

        // 订单数量
        private int _orderCount = 0;
        public int OrderCount
        {
            get => _orderCount;
            set
            {
                if (_orderCount != value)
                {
                    _orderCount = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(OrderCount)));
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(OrderCountDisplay)));
                }
            }
        }

        // 联系记录数量显示文本（确保显示数字）
        public string ContactRecordCountDisplay => ContactRecordCount.ToString();

        // 订单数量显示文本（确保显示数字）
        public string OrderCountDisplay => OrderCount.ToString();

        // 更新时间
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // 负责人姓名（优先显示销售，其次客服）
        public string ResponsiblePersonName => SalesPersonName ?? SupportPersonName ?? "未分配";

        // 负责人（简化版本）
        public string ResponsiblePerson => SalesPersonName ?? SupportPersonName ?? "未分配";

        // 行业类型（简化版本）
        public string IndustryType => IndustryTypes ?? "未分类";

        // 销售人员（简化显示）
        public string SalesPersonDisplay => SalesPersonName ?? "未分配";

        // 客服人员（简化显示）
        public string SupportPersonDisplay => SupportPersonName ?? "未分配";

        // 选中状态
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsSelected)));
            }
        }

        // 时间格式化属性
        public string FormattedCreatedAt => CreatedAt.ToString("yyyy-MM-dd HH:mm");

        public event PropertyChangedEventHandler? PropertyChanged;

        private void OnContactsChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(ContactCount)));
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(PrimaryContact)));
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(PrimaryContactName)));
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(PrimaryContactPhone)));
        }
    }
}
