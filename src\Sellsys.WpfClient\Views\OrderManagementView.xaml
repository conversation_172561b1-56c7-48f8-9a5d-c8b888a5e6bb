<UserControl x:Class="Sellsys.WpfClient.Views.OrderManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Sellsys.WpfClient.Views"
             xmlns:controls="clr-namespace:Sellsys.WpfClient.Controls"
             mc:Ignorable="d"
             d:DesignHeight="700" d:DesignWidth="1400">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Search and Filters -->
            <RowDefinition Height="*"/>    <!-- Data Grid -->
            <RowDefinition Height="Auto"/> <!-- Pagination -->
        </Grid.RowDefinitions>

        <!-- Search and Filter Section -->
        <Border Grid.Row="0" Style="{StaticResource SearchAreaBorderStyle}">
            <WrapPanel Orientation="Horizontal">
                <!-- 输入客户名称 (输入框) -->
                <TextBlock Text="输入客户名称:" Style="{StaticResource SearchLabelStyle}"/>
                <TextBox Text="{Binding SearchCustomerName, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource StandardTextBoxStyle}"
                         Width="100" Height="32" Margin="0,0,15,0"/>

                <!-- 产品名称 (下拉框) -->
                <TextBlock Text="产品名称:" Style="{StaticResource SearchLabelStyle}"/>
                <ComboBox ItemsSource="{Binding ProductOptions}"
                          SelectedItem="{Binding SelectedProduct}"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="80" Height="32" Margin="0,0,15,0"/>

                <!-- 生效日期 (日历选择器) -->
                <TextBlock Text="生效日期:" Style="{StaticResource SearchLabelStyle}"/>
                <DatePicker SelectedDate="{Binding SelectedEffectiveDateFilter}"
                            Style="{StaticResource StandardDatePickerStyle}"
                            Width="100" Height="32" Margin="0,0,15,0"/>

                <!-- 到期日期 (日历选择器) -->
                <TextBlock Text="到期日期:" Style="{StaticResource SearchLabelStyle}"/>
                <DatePicker SelectedDate="{Binding SelectedExpiryDateFilter}"
                            Style="{StaticResource StandardDatePickerStyle}"
                            Width="100" Height="32" Margin="0,0,15,0"/>

                <!-- 签单日期 (日历选择器) -->
                <TextBlock Text="签单日期:" Style="{StaticResource SearchLabelStyle}"/>
                <DatePicker SelectedDate="{Binding SelectedCreatedDateFilter}"
                            Style="{StaticResource StandardDatePickerStyle}"
                            Width="100" Height="32" Margin="0,0,15,0"/>

                <!-- 订单状态 (下拉框) -->
                <TextBlock Text="订单状态:" Style="{StaticResource SearchLabelStyle}"/>
                <ComboBox ItemsSource="{Binding StatusOptions}"
                          SelectedItem="{Binding SelectedStatus}"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="80" Height="32" Margin="0,0,15,0"/>

                <!-- 销售人员 (下拉框) -->
                <TextBlock Text="销售人员:" Style="{StaticResource SearchLabelStyle}"/>
                <ComboBox ItemsSource="{Binding SalesPersonOptions}"
                          SelectedValue="{Binding SelectedSalesPersonId}"
                          SelectedValuePath="Id"
                          DisplayMemberPath="Name"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="80" Height="32" Margin="0,0,15,0"/>

                <!-- 查询和重置按钮 -->
                <Button Content="查询"
                        Command="{Binding SearchCommand}"
                        Style="{StaticResource PrimaryButtonStyle}"/>
                <Button Content="重置"
                        Command="{Binding ResetFiltersCommand}"
                        Style="{StaticResource GrayButtonStyle}"
                        Margin="0,0,15,0"/>
            </WrapPanel>
        </Border>

        <!-- Data Grid Section -->
        <Border Grid.Row="1"
                Background="White"
                BorderBrush="{StaticResource BorderBrush}"
                BorderThickness="0,0,0,0"
                CornerRadius="0">
            <DataGrid x:Name="OrdersDataGrid"
                      ItemsSource="{Binding Orders}"
                      SelectedItem="{Binding SelectedOrder}"
                      Style="{StaticResource ResponsiveBlueHeaderDataGridStyle}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      SelectionMode="Single"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                      ScrollViewer.VerticalScrollBarVisibility="Disabled"
                      CanUserResizeRows="True">

                <!-- 行样式 -->
                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Setter Property="Background" Value="White"/>
                        <Setter Property="BorderBrush" Value="Transparent"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="MinHeight" Value="50"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F0F8FF"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="{StaticResource SelectedRowBackgroundBrush}"/>
                                <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>

                <!-- 单元格样式 -->
                <DataGrid.CellStyle>
                    <Style TargetType="DataGridCell">
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="Padding" Value="8,8"/>
                        <Setter Property="VerticalAlignment" Value="Stretch"/>
                        <Setter Property="VerticalContentAlignment" Value="Top"/>
                    </Style>
                </DataGrid.CellStyle>

                <DataGrid.Columns>
                    <!-- 序号 -->
                    <DataGridTextColumn Header="序号" Width="60" MinWidth="60" IsReadOnly="True">
                        <DataGridTextColumn.Binding>
                            <Binding RelativeSource="{RelativeSource AncestorType=DataGridRow}"
                                     Converter="{StaticResource RowIndexConverter}"/>
                        </DataGridTextColumn.Binding>
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 客户单位 -->
                    <DataGridTextColumn Header="客户单位" Binding="{Binding CustomerName}" Width="250" MinWidth="200">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Left"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="TextWrapping" Value="Wrap"/>
                                <Setter Property="Margin" Value="8,8,8,8"/>
                                <Setter Property="LineHeight" Value="22"/>
                                <Setter Property="TextTrimming" Value="None"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 产品名称 -->
                    <DataGridTextColumn Header="产品名称" Binding="{Binding ProductName}" Width="2*" MinWidth="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Left"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="TextWrapping" Value="Wrap"/>
                                <Setter Property="Margin" Value="8,8,8,8"/>
                                <Setter Property="LineHeight" Value="22"/>
                                <Setter Property="TextTrimming" Value="None"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 型号规格 -->
                    <DataGridTextColumn Header="型号规格" Binding="{Binding ModelSpecification}" Width="2*" MinWidth="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 产品单价 -->
                    <DataGridTextColumn Header="产品单价" Binding="{Binding FormattedProductPrice}" Width="*" MinWidth="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 实际单价 -->
                    <DataGridTextColumn Header="实际单价" Binding="{Binding FormattedActualPrice}" Width="*" MinWidth="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 数量 -->
                    <DataGridTextColumn Header="数量" Binding="{Binding Quantity}" Width="*" MinWidth="60">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 单位 -->
                    <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="*" MinWidth="50">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 订单金额 -->
                    <DataGridTextColumn Header="订单金额" Binding="{Binding FormattedTotalAmount}" Width="*" MinWidth="90">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 生效日期 -->
                    <DataGridTextColumn Header="生效日期" Binding="{Binding FormattedEffectiveDate}" Width="100" MinWidth="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 到期日期 -->
                    <DataGridTextColumn Header="到期日期" Binding="{Binding FormattedExpiryDate}" Width="100" MinWidth="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 签单时间 -->
                    <DataGridTextColumn Header="签单时间" Binding="{Binding FormattedCreatedAt}" Width="140" MinWidth="140">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 管理人 -->
                    <DataGridTextColumn Header="管理人" Binding="{Binding SalesPersonName}" Width="*" MinWidth="60">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 订单状态 -->
                    <DataGridTextColumn Header="订单状态" Binding="{Binding Status}" Width="*" MinWidth="80">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>


                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Pagination Control -->
        <Border Grid.Row="2"
                Background="White"
                BorderBrush="{StaticResource BorderBrush}"
                BorderThickness="0,0,0,0"
                Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧分页控件 -->
                <controls:PaginationControl Grid.Column="0" DataContext="{Binding}" HorizontalAlignment="Left"/>

                <!-- 中间合计信息 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="合计:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding OrderSummary.FormattedTotalAmount}" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    <TextBlock Text="订单数:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding OrderSummary.TotalOrders}" FontWeight="Bold" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>



        <!-- Loading Overlay -->
        <Border Grid.RowSpan="4"
                Background="#80000000"
                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="200" Height="20" Margin="0,0,0,10"/>
                <TextBlock Text="加载中..." Foreground="White" HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
