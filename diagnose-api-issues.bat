@echo off
echo ========================================
echo Sellsys API 问题诊断脚本
echo ========================================
echo.

echo 1. 检查后端服务状态...
netstat -ano | findstr :5078
if %ERRORLEVEL% EQU 0 (
    echo ✓ 后端服务正在运行在端口 5078
) else (
    echo ✗ 后端服务未运行，请先启动后端服务
    echo 运行命令: dotnet run --project src/Sellsys.WebApi
    pause
    exit /b 1
)

echo.
echo 2. 测试健康检查接口...
curl -s http://localhost:5078/health > nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ 健康检查接口正常
) else (
    echo ✗ 健康检查接口失败
)

echo.
echo 3. 测试API健康检查接口...
curl -s http://localhost:5078/api/health > nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ API健康检查接口正常
) else (
    echo ✗ API健康检查接口失败
)

echo.
echo 4. 检查已发现的问题:
echo.
echo 问题1: AuthController中的DTO不一致
echo   - 前端使用: LoginRequestDto
echo   - 后端期望: LoginRequest
echo   - 影响: 登录功能可能失败
echo.
echo 问题2: 客户查询中的复杂Include可能导致性能问题
echo   - 位置: CustomerService.GetAllCustomersAsync()
echo   - 问题: 多层Include可能导致查询超时
echo.
echo 问题3: 前端错误处理服务配置问题
echo   - 位置: App.xaml.cs 第62行
echo   - 问题: ErrorHandlingService被注册为实例而不是静态类
echo.

echo 5. 建议的修复方案:
echo.
echo 修复1: 统一登录DTO
echo   - 在Application层创建LoginRequestDto
echo   - 更新AuthController使用标准DTO
echo.
echo 修复2: 优化客户查询
echo   - 使用分页查询
echo   - 减少不必要的Include
echo.
echo 修复3: 修复依赖注入配置
echo   - 移除ErrorHandlingService的注册
echo.

echo ========================================
echo 诊断完成
echo ========================================
pause
