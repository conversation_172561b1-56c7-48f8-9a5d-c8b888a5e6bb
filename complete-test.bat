@echo off
echo ========================================
echo     Complete System Test and Startup
echo ========================================
echo.

cd /d "%~dp0"

echo 1. Checking system requirements...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo [FAIL] .NET SDK not installed
    echo Please install .NET 8.0 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
) else (
    echo [PASS] .NET SDK installed
)

echo.
echo 2. Building all projects...
dotnet build --configuration Release
if errorlevel 1 (
    echo [FAIL] Build failed
    pause
    exit /b 1
) else (
    echo [PASS] All projects built successfully
)

echo.
echo 3. Starting backend API...
echo Starting API in background...
start "Backend API" cmd /c "dotnet run --project src/Sellsys.WebApi --configuration Release"

echo Waiting for API to start...
timeout /t 10 /nobreak >nul

echo.
echo 4. Testing API connection...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5078/health' -TimeoutSec 10 -UseBasicParsing; if ($response.StatusCode -eq 200) { Write-Host '[PASS] API is running' -ForegroundColor Green } else { Write-Host '[FAIL] API not responding' -ForegroundColor Red; exit 1 } } catch { Write-Host '[FAIL] Cannot connect to API' -ForegroundColor Red; exit 1 }"

if errorlevel 1 (
    echo API test failed. Check the backend window for errors.
    pause
    exit /b 1
)

echo.
echo 5. Running API functionality test...
dotnet run --project src/ApiTester

echo.
echo 6. Opening browser windows...
echo Opening Swagger documentation...
start http://localhost:5078/swagger

echo Opening health check...
start http://localhost:5078/health

echo.
echo 7. Attempting to start WPF client...
echo Note: WPF client may not work in remote environments
echo If it fails, use the API tester above to verify functionality
echo.

start "WPF Client" cmd /c "dotnet run --project src/Sellsys.WpfClient"

echo.
echo ========================================
echo           Test Complete
echo ========================================
echo.
echo Backend API: http://localhost:5078
echo Swagger UI: http://localhost:5078/swagger
echo Health Check: http://localhost:5078/health
echo.
echo Default admin login:
echo Username: admin
echo Password: admin
echo.
echo If WPF client doesn't work, the API is fully functional
echo and can be tested through Swagger UI or the API tester.
echo.
pause
