@echo off
echo ========================================
echo 修复 SQLite 数据库损坏问题
echo ========================================
echo.

echo 问题诊断：
echo ✗ SQLite Error 11: database disk image is malformed
echo ✗ 数据库文件损坏导致所有查询失败
echo ✗ 这是前端显示500错误的根本原因
echo.

echo 解决方案：
echo 1. 停止后端服务
echo 2. 备份损坏的数据库
echo 3. 删除损坏的数据库文件
echo 4. 重新创建数据库
echo 5. 重启后端服务
echo.

echo 开始修复...
echo.

echo 1. 停止后端服务...
taskkill /f /im dotnet.exe 2>nul
echo 后端服务已停止
echo.

echo 2. 备份损坏的数据库...
if exist "src\Sellsys.WebApi\sellsys.db" (
    copy "src\Sellsys.WebApi\sellsys.db" "src\Sellsys.WebApi\sellsys.db.corrupted.backup"
    echo ✓ 已备份损坏的数据库到 sellsys.db.corrupted.backup
) else (
    echo ! 未找到数据库文件
)

echo.
echo 3. 删除损坏的数据库文件...
if exist "src\Sellsys.WebApi\sellsys.db" (
    del "src\Sellsys.WebApi\sellsys.db"
    echo ✓ 已删除损坏的数据库文件
)

if exist "src\Sellsys.WebApi\sellsys.db-shm" (
    del "src\Sellsys.WebApi\sellsys.db-shm"
    echo ✓ 已删除 WAL 共享内存文件
)

if exist "src\Sellsys.WebApi\sellsys.db-wal" (
    del "src\Sellsys.WebApi\sellsys.db-wal"
    echo ✓ 已删除 WAL 日志文件
)

echo.
echo 4. 重新创建数据库...
echo 启动后端服务以自动创建新数据库...
start "Sellsys Backend" cmd /k "cd /d %~dp0 && dotnet run --project src/Sellsys.WebApi"

echo.
echo 等待数据库初始化...
timeout /t 15 /nobreak > nul

echo.
echo 5. 测试数据库连接...
curl -s http://localhost:5078/health > nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ 数据库修复成功！
    echo ✓ 后端服务正常运行
    echo ✓ 现在可以启动前端应用了
) else (
    echo ✗ 数据库修复可能失败，请检查后端日志
)

echo.
echo ========================================
echo 修复完成
echo ========================================
echo.
echo 下一步：
echo 1. 启动前端应用：dotnet run --project src/Sellsys.WpfClient
echo 2. 使用默认管理员账号登录：admin / admin
echo 3. 重新添加必要的数据
echo.
pause
