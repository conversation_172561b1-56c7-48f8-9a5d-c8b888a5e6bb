using System.ComponentModel.DataAnnotations;

namespace Sellsys.WpfClient.Models
{
    public class ProductUpsertDto
    {
        [Required]
        [StringLength(255)]
        public string Name { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Specification { get; set; }

        [StringLength(20)]
        public string? Unit { get; set; }

        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal ListPrice { get; set; }

        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal MinPrice { get; set; }

        [Range(0, double.MaxValue)]
        public decimal? SalesCommission { get; set; }

        [Range(0, double.MaxValue)]
        public decimal? SupervisorCommission { get; set; }

        [Range(0, double.MaxValue)]
        public decimal? ManagerCommission { get; set; }
    }
}
