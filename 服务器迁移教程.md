# Sellsys 服务器迁移教程

## 📋 概述

本教程将指导您如何将 Sellsys 销售管理系统迁移到新的服务器，包括数据备份、服务器配置、部署和客户端更新等完整流程。

## ⚠️ 重要提醒

**在开始迁移前，请务必：**
1. 备份当前服务器的所有数据
2. 确保新服务器满足系统要求
3. 准备好充足的时间进行迁移（建议在业务低峰期进行）
4. 通知所有用户迁移时间安排

---

## 🔧 新服务器要求

### 硬件要求
- **CPU**: 2核心或以上
- **内存**: 4GB RAM 或以上
- **存储**: 20GB 可用空间或以上
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Ubuntu 20.04+ 或 CentOS 8+
- **权限**: root 访问权限
- **端口**: 开放 80、443、5000 端口

---

## 📦 第一步：备份当前服务器数据

### 1.1 连接到当前服务器
```bash
ssh root@当前服务器IP
```

### 1.2 备份数据库
```bash
# 停止服务
sudo systemctl stop sellsys-webapi

# 备份数据库文件
sudo cp /opt/sellsys/sellsys.db /opt/sellsys/backup/sellsys_migration_$(date +%Y%m%d_%H%M%S).db

# 下载备份到本地
scp root@当前服务器IP:/opt/sellsys/sellsys.db ./sellsys_backup.db
```

### 1.3 备份配置文件（可选）
```bash
# 备份应用配置
sudo cp /opt/sellsys/appsettings.json ./appsettings_backup.json

# 备份服务配置
sudo cp /etc/systemd/system/sellsys-webapi.service ./sellsys-webapi.service.backup
```

---

## 🖥️ 第二步：准备新服务器

### 2.1 连接到新服务器
```bash
ssh root@新服务器IP
```

### 2.2 更新系统
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

### 2.3 安装必要软件
```bash
# Ubuntu/Debian
sudo apt install -y nginx curl wget unzip

# CentOS/RHEL
sudo yum install -y nginx curl wget unzip
```

---

## 📤 第三步：上传部署文件

### 3.1 上传部署包
```bash
# 从本地上传部署包到新服务器
scp -r deploy-package/* root@新服务器IP:~/sellsys-deploy/
```

### 3.2 上传数据库备份
```bash
# 上传备份的数据库文件
scp sellsys_backup.db root@新服务器IP:~/sellsys-deploy/
```

---

## 🚀 第四步：部署到新服务器

### 4.1 配置环境
```bash
# 在新服务器上执行
cd ~/sellsys-deploy
bash setup-environment.sh
```

### 4.2 部署应用
```bash
# 执行部署脚本
bash deploy.sh
```

### 4.3 恢复数据库
```bash
# 停止服务
sudo systemctl stop sellsys-webapi

# 替换数据库文件
sudo cp ~/sellsys-deploy/sellsys_backup.db /opt/sellsys/sellsys.db
sudo chown sellsys:sellsys /opt/sellsys/sellsys.db

# 启动服务
sudo systemctl start sellsys-webapi
```

---

## ✅ 第五步：验证部署

### 5.1 检查服务状态
```bash
# 检查服务是否运行
sudo systemctl status sellsys-webapi

# 检查端口监听
sudo netstat -tlnp | grep :5000
```

### 5.2 测试API接口
```bash
# 健康检查
curl http://localhost:5000/health

# 从外部测试
curl http://新服务器IP:5000/health
```

### 5.3 检查日志
```bash
# 查看应用日志
sudo journalctl -u sellsys-webapi -f
```

---

## 🔄 第六步：更新客户端配置

### 6.1 修改API地址配置

需要修改客户端代码中的服务器地址：

**文件位置**: `src/Sellsys.WpfClient/Services/ApiService.cs`

```csharp
// 修改生产环境URL为新服务器地址
private const string ProductionUrl = "http://新服务器IP:5000/api";
```

### 6.2 重新编译客户端
```bash
# 编译新的客户端版本
dotnet publish src/Sellsys.WpfClient/Sellsys.WpfClient.csproj \
    -c Release \
    -o publish/wpfclient-new \
    --self-contained true \
    -r win-x64
```

### 6.3 创建新的客户端包
```bash
# 创建新的客户端发布包
PACKAGE_NAME="Sellsys-Client-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$PACKAGE_NAME"
cp -r publish/wpfclient-new/* "$PACKAGE_NAME"/

# 创建说明文件（更新服务器地址）
# 创建压缩包
tar -czf "$PACKAGE_NAME.tar.gz" "$PACKAGE_NAME"
```

---

## 📱 第七步：通知用户更新

### 7.1 准备更新通知
```
主题：系统服务器迁移通知

尊敬的用户：

我们已将 Sellsys 销售管理系统迁移到新服务器，请下载并安装最新版本的客户端。

新服务器信息：
- 服务器地址：http://新服务器IP:5000
- 迁移时间：[具体时间]

请下载新版客户端：[下载链接]

如有问题，请联系技术支持。
```

### 7.2 分发新客户端
- 将新的客户端包发送给所有用户
- 提供详细的安装说明
- 确保用户卸载旧版本后再安装新版本

---

## 🔒 第八步：安全配置（推荐）

### 8.1 配置防火墙
```bash
# 配置 UFW 防火墙
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 5000
sudo ufw --force enable
```

### 8.2 配置SSL证书（可选）
```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书（需要域名）
sudo certbot --nginx -d yourdomain.com
```

---

## 🗑️ 第九步：清理旧服务器

**⚠️ 确认新服务器运行正常后再执行**

### 9.1 备份验证
```bash
# 确认数据已完整迁移
# 确认所有用户已更新客户端
# 确认业务运行正常
```

### 9.2 停止旧服务器服务
```bash
# 在旧服务器上
sudo systemctl stop sellsys-webapi
sudo systemctl disable sellsys-webapi
```

### 9.3 保留备份
```bash
# 保留重要备份文件至少30天
# 建议将备份下载到本地保存
```

---

## 🆘 故障排除

### 常见问题及解决方案

#### 1. 服务无法启动
```bash
# 检查日志
sudo journalctl -u sellsys-webapi --no-pager -n 50

# 检查端口占用
sudo netstat -tlnp | grep :5000

# 检查文件权限
sudo chown -R sellsys:sellsys /opt/sellsys
sudo chmod +x /opt/sellsys/Sellsys.WebApi
```

#### 2. 数据库问题
```bash
# 检查数据库文件
ls -la /opt/sellsys/sellsys.db

# 重新恢复数据库
sudo systemctl stop sellsys-webapi
sudo cp backup/sellsys_backup.db /opt/sellsys/sellsys.db
sudo chown sellsys:sellsys /opt/sellsys/sellsys.db
sudo systemctl start sellsys-webapi
```

#### 3. 网络连接问题
```bash
# 检查防火墙
sudo ufw status

# 检查端口监听
sudo ss -tlnp | grep :5000

# 测试外部访问
curl -I http://服务器IP:5000/health
```

---

## 📞 技术支持

如果在迁移过程中遇到问题，请联系技术支持：

1. **准备信息**：
   - 服务器IP地址
   - 错误信息截图
   - 系统日志

2. **联系方式**：
   - 提供具体的联系方式

3. **远程协助**：
   - 可提供远程技术支持服务

---

## 📝 迁移检查清单

- [ ] 备份当前服务器数据
- [ ] 准备新服务器环境
- [ ] 上传部署文件和数据备份
- [ ] 执行部署脚本
- [ ] 恢复数据库
- [ ] 验证服务运行状态
- [ ] 测试API接口
- [ ] 更新客户端配置
- [ ] 重新编译客户端
- [ ] 创建新客户端包
- [ ] 通知用户更新
- [ ] 分发新客户端
- [ ] 配置安全设置
- [ ] 清理旧服务器
- [ ] 保留备份文件

---

**迁移完成后，请保留此文档以备将来参考。**
