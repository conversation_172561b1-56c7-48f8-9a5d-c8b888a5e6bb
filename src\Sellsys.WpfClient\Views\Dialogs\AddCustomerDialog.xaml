<Window x:Class="Sellsys.WpfClient.Views.Dialogs.AddCustomerDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="添加客户"
        Height="950" Width="1450"
        MinHeight="600" MinWidth="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        ShowInTaskbar="False">
    
    <Window.Resources>
        <!-- 弹窗样式 -->
        <Style x:Key="DialogTitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="15,0"/>
        </Style>
        
        <Style x:Key="DialogLabelStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#303133"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/> <!-- Title Bar -->
            <RowDefinition Height="*"/>  <!-- Content -->
            <RowDefinition Height="80" MinHeight="80"/> <!-- Buttons -->
        </Grid.RowDefinitions>

        <!-- Title Bar -->
        <Border Grid.Row="0" Background="{StaticResource PopupTitleBrush}">
            <TextBlock Text="添加客户" Style="{StaticResource DialogTitleStyle}"/>
        </Border>

        <!-- Content Area -->
        <ScrollViewer Grid.Row="1"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Padding="20"
                      Margin="0,0,0,5">
            <StackPanel>
                <!-- 行业类别 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="行业类别：" Style="{StaticResource DialogLabelStyle}"/>
                    <ComboBox Grid.Column="1" 
                              x:Name="IndustryTypeComboBox"
                              ItemsSource="{Binding IndustryTypes}"
                              SelectedItem="{Binding SelectedIndustryType}"
                              Style="{StaticResource StandardComboBoxStyle}"/>
                </Grid>

                <!-- 客户单位 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="客户单位：" Style="{StaticResource DialogLabelStyle}"/>
                    <TextBox Grid.Column="1" 
                             Text="{Binding CustomerName, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource StandardTextBoxStyle}"/>
                </Grid>

                <!-- 省份和城市 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="所在地区：" Style="{StaticResource DialogLabelStyle}"/>
                    <ComboBox Grid.Column="1" 
                              ItemsSource="{Binding Provinces}"
                              SelectedItem="{Binding SelectedProvince}"
                              Style="{StaticResource StandardComboBoxStyle}"/>
                    <ComboBox Grid.Column="3" 
                              ItemsSource="{Binding Cities}"
                              SelectedItem="{Binding SelectedCity}"
                              Style="{StaticResource StandardComboBoxStyle}"/>
                </Grid>

                <!-- 详细地址 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="详细地址：" Style="{StaticResource DialogLabelStyle}"/>
                    <TextBox Grid.Column="1" 
                             Text="{Binding DetailedAddress, UpdateSourceTrigger=PropertyChanged}"
                             Style="{StaticResource StandardTextBoxStyle}"/>
                </Grid>

                <!-- 客户备注 -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="客户备注：" Style="{StaticResource DialogLabelStyle}" VerticalAlignment="Top" Margin="0,5,10,0"/>
                    <TextBox Grid.Column="1"
                             Text="{Binding CustomerRemarks, UpdateSourceTrigger=PropertyChanged}"
                             TextWrapping="Wrap"
                             AcceptsReturn="True"
                             Height="80"
                             VerticalScrollBarVisibility="Auto"
                             VerticalContentAlignment="Top"
                             HorizontalContentAlignment="Left"
                             Style="{StaticResource StandardTextBoxStyle}"/>
                </Grid>

                <!-- 联系人列表 -->
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="联系人：" Style="{StaticResource DialogLabelStyle}" Margin="0,0,0,10"/>

                    <Border Grid.Row="1" BorderBrush="{StaticResource BorderBrush}" BorderThickness="1" MinHeight="200" Background="White">
                        <ItemsControl ItemsSource="{Binding Contacts}" Margin="15">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border BorderBrush="{StaticResource BorderBrush}"
                                            BorderThickness="0,0,0,1"
                                            Padding="0,10"
                                            Margin="0,5">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="80"/>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="20"/>
                                                <ColumnDefinition Width="60"/>
                                                <ColumnDefinition Width="130"/>
                                                <ColumnDefinition Width="20"/>
                                                <ColumnDefinition Width="80"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" Text="联系人：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                                            <TextBox Grid.Column="1"
                                                     Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                                                     Style="{StaticResource StandardTextBoxStyle}"/>

                                            <TextBlock Grid.Column="3" Text="电话：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                                            <TextBox Grid.Column="4"
                                                     Text="{Binding Phone, UpdateSourceTrigger=PropertyChanged}"
                                                     Style="{StaticResource StandardTextBoxStyle}"/>

                                            <CheckBox Grid.Column="6"
                                                      IsChecked="{Binding IsPrimary}"
                                                      Content="关键人"
                                                      VerticalAlignment="Center"
                                                      FontSize="14"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Border>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- Button Area -->
        <Border Grid.Row="2" Background="#F8F9FA" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="20,22,20,22" VerticalAlignment="Center">
                <Button Content="取消"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource DeleteButtonStyle}"
                        Width="80"
                        Height="35"
                        Margin="0,0,10,0"
                        VerticalAlignment="Center"/>
                <Button Command="{Binding SaveCommand}"
                        Width="80"
                        Height="35"
                        Margin="0"
                        VerticalAlignment="Center">
                    <Button.Style>
                        <Style TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
                            <Setter Property="Content" Value="保存"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Content" Value="保存中..."/>
                                    <Setter Property="IsEnabled" Value="False"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
