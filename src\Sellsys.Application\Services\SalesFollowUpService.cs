using Microsoft.EntityFrameworkCore;
using Sellsys.Application.DTOs.SalesFollowUp;
using Sellsys.Application.Interfaces;
using Sellsys.Application.Common;
using Sellsys.CrossCutting.Common;
using Sellsys.Domain.Entities;
using Sellsys.Infrastructure.Data;
using System.Net;

namespace Sellsys.Application.Services
{
    public class SalesFollowUpService : ISalesFollowUpService
    {
        private readonly SellsysDbContext _context;

        public SalesFollowUpService(SellsysDbContext context)
        {
            _context = context;
        }

        public async Task<ApiResponse<List<SalesFollowUpLogDto>>> GetAllSalesFollowUpLogsAsync()
        {
            try
            {
                var logs = await _context.SalesFollowUpLogs
                    .Include(s => s.Customer)
                    .Include(s => s.Contact)
                    .Include(s => s.SalesPerson)
                    .OrderByDescending(s => s.CreatedAt)
                    .Select(s => MapToDto(s))
                    .ToListAsync();

                return new ApiResponse<List<SalesFollowUpLogDto>>
                {
                    IsSuccess = true,
                    Data = logs,
                    Message = "获取销售跟进记录成功"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<List<SalesFollowUpLogDto>>
                {
                    IsSuccess = false,
                    Message = $"获取销售跟进记录失败: {ex.Message}",
                    StatusCode = HttpStatusCode.InternalServerError
                };
            }
        }

        /// <summary>
        /// 根据用户权限获取销售跟进记录列表
        /// </summary>
        /// <param name="userId">当前用户ID，如果为null则返回所有记录（管理员权限）</param>
        /// <returns>过滤后的销售跟进记录列表</returns>
        public async Task<ApiResponse<List<SalesFollowUpLogDto>>> GetSalesFollowUpLogsWithPermissionAsync(int? userId = null)
        {
            try
            {
                // 如果没有传递用户ID，返回所有记录（管理员权限）
                if (!userId.HasValue)
                {
                    return await GetAllSalesFollowUpLogsAsync();
                }

                // 如果是admin用户（ID为0），直接返回所有记录
                if (userId.Value == 0)
                {
                    return await GetAllSalesFollowUpLogsAsync();
                }

                // 获取当前用户信息
                var currentUser = await _context.Employees
                    .Include(e => e.Group)
                        .ThenInclude(g => g!.Department)
                    .Include(e => e.Role)
                    .FirstOrDefaultAsync(e => e.Id == userId.Value);

                if (currentUser == null)
                {
                    return new ApiResponse<List<SalesFollowUpLogDto>>
                    {
                        IsSuccess = false,
                        Message = "用户不存在",
                        StatusCode = HttpStatusCode.Unauthorized
                    };
                }

                // 构建基础查询
                var query = _context.SalesFollowUpLogs
                    .Include(s => s.Customer)
                    .Include(s => s.Contact)
                    .Include(s => s.SalesPerson)
                        .ThenInclude(sp => sp!.Group)
                    .AsQueryable();

                // 根据用户部门和角色进行权限过滤
                var departmentName = currentUser.Group?.Department?.Name;
                var roleLevel = GetRoleLevel(currentUser.Role?.Name);

                if (departmentName == "销售部")
                {
                    query = ApplySalesFollowUpPermissionFilter(query, currentUser, roleLevel);
                }
                else
                {
                    // 其他部门暂时不显示销售跟进记录
                    query = query.Where(s => false);
                }

                var logs = await query
                    .OrderByDescending(s => s.CreatedAt)
                    .Select(s => MapToDto(s))
                    .ToListAsync();

                return new ApiResponse<List<SalesFollowUpLogDto>>
                {
                    IsSuccess = true,
                    Data = logs,
                    Message = "获取销售跟进记录成功"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<List<SalesFollowUpLogDto>>
                {
                    IsSuccess = false,
                    Message = $"获取销售跟进记录失败: {ex.Message}",
                    StatusCode = HttpStatusCode.InternalServerError
                };
            }
        }

        public async Task<ApiResponse<SalesFollowUpLogDto>> GetSalesFollowUpLogByIdAsync(int id)
        {
            try
            {
                var log = await _context.SalesFollowUpLogs
                    .Include(s => s.Customer)
                    .Include(s => s.Contact)
                    .Include(s => s.SalesPerson)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (log == null)
                {
                    return new ApiResponse<SalesFollowUpLogDto>
                    {
                        IsSuccess = false,
                        Message = "销售跟进记录不存在",
                        StatusCode = HttpStatusCode.NotFound
                    };
                }

                var dto = MapToDto(log);

                return new ApiResponse<SalesFollowUpLogDto>
                {
                    IsSuccess = true,
                    Data = dto,
                    Message = "获取销售跟进记录成功"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<SalesFollowUpLogDto>
                {
                    IsSuccess = false,
                    Message = $"获取销售跟进记录失败: {ex.Message}",
                    StatusCode = HttpStatusCode.InternalServerError
                };
            }
        }

        public async Task<ApiResponse<List<SalesFollowUpLogDto>>> GetSalesFollowUpLogsByCustomerIdAsync(int customerId)
        {
            try
            {
                var logs = await _context.SalesFollowUpLogs
                    .Include(s => s.Customer)
                    .Include(s => s.Contact)
                    .Include(s => s.SalesPerson)
                    .Where(s => s.CustomerId == customerId)
                    .OrderByDescending(s => s.CreatedAt)
                    .Select(s => MapToDto(s))
                    .ToListAsync();

                return new ApiResponse<List<SalesFollowUpLogDto>>
                {
                    IsSuccess = true,
                    Data = logs,
                    Message = "获取客户销售跟进记录成功"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<List<SalesFollowUpLogDto>>
                {
                    IsSuccess = false,
                    Message = $"获取客户销售跟进记录失败: {ex.Message}",
                    StatusCode = HttpStatusCode.InternalServerError
                };
            }
        }

        /// <summary>
        /// 根据客户ID和用户权限获取销售跟进记录列表
        /// </summary>
        /// <param name="customerId">客户ID</param>
        /// <param name="userId">当前用户ID，如果为null则返回所有记录（管理员权限）</param>
        /// <returns>过滤后的销售跟进记录列表</returns>
        public async Task<ApiResponse<List<SalesFollowUpLogDto>>> GetSalesFollowUpLogsByCustomerIdWithPermissionAsync(int customerId, int? userId = null)
        {
            try
            {
                // 如果没有传递用户ID，返回所有记录（管理员权限）
                if (!userId.HasValue)
                {
                    return await GetSalesFollowUpLogsByCustomerIdAsync(customerId);
                }

                // 如果是admin用户（ID为0），直接返回所有记录
                if (userId.Value == 0)
                {
                    return await GetSalesFollowUpLogsByCustomerIdAsync(customerId);
                }

                // 获取当前用户信息
                var currentUser = await _context.Employees
                    .Include(e => e.Group)
                        .ThenInclude(g => g!.Department)
                    .Include(e => e.Role)
                    .FirstOrDefaultAsync(e => e.Id == userId.Value);

                if (currentUser == null)
                {
                    return new ApiResponse<List<SalesFollowUpLogDto>>
                    {
                        IsSuccess = false,
                        Message = $"用户不存在 (用户ID: {userId.Value})，请重新登录",
                        StatusCode = HttpStatusCode.Unauthorized
                    };
                }

                // 首先检查用户是否有权限查看该客户
                var customer = await _context.Customers
                    .Include(c => c.SalesPerson)
                        .ThenInclude(sp => sp!.Group)
                    .FirstOrDefaultAsync(c => c.Id == customerId);

                if (customer == null)
                {
                    return new ApiResponse<List<SalesFollowUpLogDto>>
                    {
                        IsSuccess = false,
                        Message = "客户不存在",
                        StatusCode = HttpStatusCode.NotFound
                    };
                }

                // 检查客户访问权限
                var departmentName = currentUser.Group?.Department?.Name;
                var roleLevel = GetRoleLevel(currentUser.Role?.Name);

                bool hasCustomerAccess = false;

                if (departmentName == "销售部")
                {
                    if (roleLevel == RoleLevel.Staff)
                    {
                        // 普通销售：只能看到分配给自己的客户
                        hasCustomerAccess = customer.SalesPersonId == currentUser.Id;
                    }
                    else if (roleLevel == RoleLevel.Supervisor)
                    {
                        // 销售主管：可以看到所有组的客户资源
                        hasCustomerAccess = true;
                    }
                    else if (roleLevel == RoleLevel.Manager)
                    {
                        // 销售经理：可以看到同组销售的客户 + 未分配销售的客户
                        hasCustomerAccess = customer.SalesPersonId == null ||
                                          (customer.SalesPerson != null && customer.SalesPerson.GroupId == currentUser.GroupId);
                    }
                }

                if (!hasCustomerAccess)
                {
                    return new ApiResponse<List<SalesFollowUpLogDto>>
                    {
                        IsSuccess = false,
                        Message = "无权限查看该客户的销售跟进记录",
                        StatusCode = HttpStatusCode.Forbidden
                    };
                }

                // 如果有权限，返回该客户的所有销售跟进记录
                return await GetSalesFollowUpLogsByCustomerIdAsync(customerId);
            }
            catch (Exception ex)
            {
                return new ApiResponse<List<SalesFollowUpLogDto>>
                {
                    IsSuccess = false,
                    Message = $"获取客户销售跟进记录失败: {ex.Message}",
                    StatusCode = HttpStatusCode.InternalServerError
                };
            }
        }

        public async Task<ApiResponse<SalesFollowUpLogDto>> CreateSalesFollowUpLogAsync(SalesFollowUpLogUpsertDto logDto)
        {
            try
            {
                // 验证客户是否存在
                var customerExists = await _context.Customers.AnyAsync(c => c.Id == logDto.CustomerId);
                if (!customerExists)
                {
                    return new ApiResponse<SalesFollowUpLogDto>
                    {
                        IsSuccess = false,
                        Message = "指定的客户不存在",
                        StatusCode = HttpStatusCode.BadRequest
                    };
                }

                // 如果指定了联系人，验证联系人是否属于该客户
                if (logDto.ContactId.HasValue)
                {
                    var contactExists = await _context.Contacts
                        .AnyAsync(c => c.Id == logDto.ContactId.Value && c.CustomerId == logDto.CustomerId);
                    if (!contactExists)
                    {
                        return new ApiResponse<SalesFollowUpLogDto>
                        {
                            IsSuccess = false,
                            Message = "指定的联系人不属于该客户",
                            StatusCode = HttpStatusCode.BadRequest
                        };
                    }
                }

                // 如果指定了销售人员，验证销售人员是否存在
                if (logDto.SalesPersonId.HasValue)
                {
                    // Admin用户(ID=0)拥有特殊权限，跳过验证
                    if (logDto.SalesPersonId.Value != 0)
                    {
                        var salesPersonExists = await _context.Employees.AnyAsync(e => e.Id == logDto.SalesPersonId.Value);
                        if (!salesPersonExists)
                        {
                            return new ApiResponse<SalesFollowUpLogDto>
                            {
                                IsSuccess = false,
                                Message = "指定的销售人员不存在",
                                StatusCode = HttpStatusCode.BadRequest
                            };
                        }
                    }
                }

                var log = new SalesFollowUpLog
                {
                    CustomerId = logDto.CustomerId,
                    ContactId = logDto.ContactId,
                    Summary = logDto.Summary,
                    CustomerIntention = logDto.CustomerIntention,
                    NextFollowUpDate = logDto.NextFollowUpDate,
                    SalesPersonId = logDto.SalesPersonId,
                    CreatedAt = DateTime.UtcNow
                };

                _context.SalesFollowUpLogs.Add(log);
                await _context.SaveChangesAsync();

                // 重新查询以获取关联数据
                var createdLog = await _context.SalesFollowUpLogs
                    .Include(s => s.Customer)
                    .Include(s => s.Contact)
                    .Include(s => s.SalesPerson)
                    .FirstOrDefaultAsync(s => s.Id == log.Id);

                var dto = MapToDto(createdLog!);

                return new ApiResponse<SalesFollowUpLogDto>
                {
                    IsSuccess = true,
                    Data = dto,
                    Message = "创建销售跟进记录成功"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<SalesFollowUpLogDto>
                {
                    IsSuccess = false,
                    Message = $"创建销售跟进记录失败: {ex.Message}",
                    StatusCode = HttpStatusCode.InternalServerError
                };
            }
        }

        public async Task<ApiResponse> UpdateSalesFollowUpLogAsync(int id, SalesFollowUpLogUpsertDto logDto)
        {
            try
            {
                var log = await _context.SalesFollowUpLogs.FindAsync(id);
                if (log == null)
                {
                    return new ApiResponse
                    {
                        IsSuccess = false,
                        Message = "销售跟进记录不存在",
                        StatusCode = HttpStatusCode.NotFound
                    };
                }

                // 验证客户是否存在
                var customerExists = await _context.Customers.AnyAsync(c => c.Id == logDto.CustomerId);
                if (!customerExists)
                {
                    return new ApiResponse
                    {
                        IsSuccess = false,
                        Message = "指定的客户不存在",
                        StatusCode = HttpStatusCode.BadRequest
                    };
                }

                // 如果指定了联系人，验证联系人是否属于该客户
                if (logDto.ContactId.HasValue)
                {
                    var contactExists = await _context.Contacts
                        .AnyAsync(c => c.Id == logDto.ContactId.Value && c.CustomerId == logDto.CustomerId);
                    if (!contactExists)
                    {
                        return new ApiResponse
                        {
                            IsSuccess = false,
                            Message = "指定的联系人不属于该客户",
                            StatusCode = HttpStatusCode.BadRequest
                        };
                    }
                }

                // 如果指定了销售人员，验证销售人员是否存在
                if (logDto.SalesPersonId.HasValue)
                {
                    var salesPersonExists = await _context.Employees.AnyAsync(e => e.Id == logDto.SalesPersonId.Value);
                    if (!salesPersonExists)
                    {
                        return new ApiResponse
                        {
                            IsSuccess = false,
                            Message = "指定的销售人员不存在",
                            StatusCode = HttpStatusCode.BadRequest
                        };
                    }
                }

                // 更新字段
                log.CustomerId = logDto.CustomerId;
                log.ContactId = logDto.ContactId;
                log.Summary = logDto.Summary;
                log.CustomerIntention = logDto.CustomerIntention;
                log.NextFollowUpDate = logDto.NextFollowUpDate;
                log.SalesPersonId = logDto.SalesPersonId;

                await _context.SaveChangesAsync();

                return new ApiResponse
                {
                    IsSuccess = true,
                    Message = "更新销售跟进记录成功"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse
                {
                    IsSuccess = false,
                    Message = $"更新销售跟进记录失败: {ex.Message}",
                    StatusCode = HttpStatusCode.InternalServerError
                };
            }
        }

        public async Task<ApiResponse> DeleteSalesFollowUpLogAsync(int id)
        {
            try
            {
                var log = await _context.SalesFollowUpLogs.FindAsync(id);
                if (log == null)
                {
                    return new ApiResponse
                    {
                        IsSuccess = false,
                        Message = "销售跟进记录不存在",
                        StatusCode = HttpStatusCode.NotFound
                    };
                }

                _context.SalesFollowUpLogs.Remove(log);
                await _context.SaveChangesAsync();

                return new ApiResponse
                {
                    IsSuccess = true,
                    Message = "删除销售跟进记录成功"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse
                {
                    IsSuccess = false,
                    Message = $"删除销售跟进记录失败: {ex.Message}",
                    StatusCode = HttpStatusCode.InternalServerError
                };
            }
        }

        /// <summary>
        /// 获取角色级别
        /// </summary>
        private RoleLevel GetRoleLevel(string? roleName)
        {
            if (string.IsNullOrEmpty(roleName))
                return RoleLevel.Staff;

            if (roleName.Contains("经理"))
                return RoleLevel.Manager;
            else if (roleName.Contains("主管"))
                return RoleLevel.Supervisor;
            else
                return RoleLevel.Staff;
        }

        /// <summary>
        /// 应用销售跟进记录权限过滤
        /// </summary>
        private IQueryable<SalesFollowUpLog> ApplySalesFollowUpPermissionFilter(IQueryable<SalesFollowUpLog> query, Employee currentUser, RoleLevel roleLevel)
        {
            if (roleLevel == RoleLevel.Staff)
            {
                // 普通销售：只能看到自己名下的销售跟进记录，以及同组的销售跟进记录
                return query.Where(s =>
                    s.SalesPersonId == currentUser.Id || // 自己的记录
                    (s.SalesPerson != null && s.SalesPerson.GroupId == currentUser.GroupId) // 同组的记录
                );
            }
            else if (roleLevel == RoleLevel.Supervisor || roleLevel == RoleLevel.Manager)
            {
                // 销售主管和经理：可以看到全部的销售跟进记录
                return query;
            }

            return query.Where(s => false);
        }

        private static SalesFollowUpLogDto MapToDto(SalesFollowUpLog log)
        {
            return new SalesFollowUpLogDto
            {
                Id = log.Id,
                CustomerId = log.CustomerId,
                CustomerName = log.Customer?.Name ?? string.Empty,
                ContactId = log.ContactId,
                ContactName = log.Contact?.Name,
                ContactPhone = log.Contact?.Phone,
                Summary = log.Summary,
                CustomerIntention = log.CustomerIntention,
                NextFollowUpDate = log.NextFollowUpDate,
                SalesPersonId = log.SalesPersonId,
                SalesPersonName = log.SalesPerson?.Name ?? "未分配",
                CreatedAt = log.CreatedAt
            };
        }
    }
}
