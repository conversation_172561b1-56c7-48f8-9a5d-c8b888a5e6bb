<Window x:Class="Sellsys.WpfClient.Views.Dialogs.EditOrderDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="编辑订单"
        Height="500"
        Width="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#409EFF" Padding="15">
            <StackPanel>
                <TextBlock Text="编辑订单" 
                           Foreground="White" 
                           FontSize="18" 
                           FontWeight="Bold"/>
                <TextBlock Text="{Binding CustomerName, StringFormat='客户单位：{0}'}" 
                           Foreground="White" 
                           FontSize="14" 
                           Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Column -->
                <StackPanel Grid.Column="0">
                    <!-- 订单编号 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="订单编号：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <Border Grid.Column="1" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="1" Padding="8">
                            <TextBlock Text="{Binding OrderNumber}" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        </Border>
                    </Grid>

                    <!-- 客户名称 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="客户名称：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <Border Grid.Column="1" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="1" Padding="8">
                            <TextBlock Text="{Binding CustomerName}" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        </Border>
                    </Grid>

                    <!-- 生效日期 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="生效日期：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <DatePicker Grid.Column="1"
                                    SelectedDate="{Binding EffectiveDate}"
                                    Style="{StaticResource StandardDatePickerStyle}"
                                    Height="32"/>
                    </Grid>

                    <!-- 到期日期 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="到期日期：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <DatePicker Grid.Column="1"
                                    SelectedDate="{Binding ExpiryDate}"
                                    Style="{StaticResource StandardDatePickerStyle}"
                                    Height="32"/>
                    </Grid>

                    <!-- 销售姓名 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="销售姓名：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <Border Grid.Column="1" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="1" Padding="8">
                            <TextBlock Text="{Binding SalesPersonName}" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        </Border>
                    </Grid>
                </StackPanel>

                <!-- Right Column -->
                <StackPanel Grid.Column="2">
                    <!-- 订单状态 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="订单状态：" VerticalAlignment="Top" Style="{StaticResource DialogLabelStyle}" Margin="0,8,0,0"/>
                        <StackPanel Grid.Column="1" Orientation="Vertical">
                            <RadioButton Content="待收款"
                                         IsChecked="{Binding IsPendingPayment}"
                                         GroupName="OrderStatus"
                                         Style="{StaticResource DialogRadioButtonStyle}"
                                         Margin="0,0,0,10"
                                         VerticalAlignment="Center"/>
                            <RadioButton Content="已收款"
                                         IsChecked="{Binding IsPaid}"
                                         GroupName="OrderStatus"
                                         Style="{StaticResource DialogRadioButtonStyle}"
                                         VerticalAlignment="Center"/>
                        </StackPanel>
                    </Grid>

                    <!-- 创建时间 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="创建时间：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        <Border Grid.Column="1" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="1" Padding="8">
                            <TextBlock Text="{Binding CreatedAt, StringFormat='yyyy-MM-dd HH:mm:ss'}" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                        </Border>
                    </Grid>

                    <!-- 备注 -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="备注：" VerticalAlignment="Top" Style="{StaticResource DialogLabelStyle}" Margin="0,8,0,0"/>
                        <TextBox Grid.Column="1"
                                 Text="{Binding Remarks}"
                                 Style="{StaticResource StandardTextBoxStyle}"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 Height="80"
                                 VerticalScrollBarVisibility="Auto"/>
                    </Grid>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Padding="15" Background="#F8F9FA">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="取消"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource GrayButtonStyle}"
                        Width="80"
                        Height="32"
                        Margin="0,0,10,0"/>
                <Button Content="保存"
                        Command="{Binding SaveCommand}"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Width="80"
                        Height="32"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
