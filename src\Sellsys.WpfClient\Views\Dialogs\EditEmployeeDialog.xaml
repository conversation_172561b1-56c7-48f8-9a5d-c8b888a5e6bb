<Window x:Class="Sellsys.WpfClient.Views.Dialogs.EditEmployeeDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="编辑员工" Height="500" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0" Text="编辑员工信息" 
                   FontSize="16" FontWeight="Bold" 
                   Foreground="#333333" 
                   Margin="0,0,0,20"/>

        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Department Name -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="部门名称:" 
                           VerticalAlignment="Center" 
                           Margin="0,0,10,15" 
                           FontWeight="Medium"/>
                <ComboBox Grid.Row="0" Grid.Column="1" 
                          ItemsSource="{Binding Departments}"
                          SelectedItem="{Binding SelectedDepartment}"
                          DisplayMemberPath="Name"
                          Height="30" 
                          Padding="8,5"
                          BorderBrush="#CCCCCC"
                          BorderThickness="1"
                          Margin="0,0,0,15"/>

                <!-- Department Group -->
                <TextBlock Grid.Row="1" Grid.Column="0" Text="部门分组:" 
                           VerticalAlignment="Center" 
                           Margin="0,0,10,15" 
                           FontWeight="Medium"/>
                <ComboBox Grid.Row="1" Grid.Column="1" 
                          ItemsSource="{Binding DepartmentGroups}"
                          SelectedItem="{Binding SelectedDepartmentGroup}"
                          DisplayMemberPath="Name"
                          Height="30" 
                          Padding="8,5"
                          BorderBrush="#CCCCCC"
                          BorderThickness="1"
                          Margin="0,0,0,15"/>

                <!-- Employee Name -->
                <TextBlock Grid.Row="2" Grid.Column="0" Text="员工姓名:" 
                           VerticalAlignment="Center" 
                           Margin="0,0,10,15" 
                           FontWeight="Medium"/>
                <TextBox Grid.Row="2" Grid.Column="1" 
                         Text="{Binding EmployeeName, UpdateSourceTrigger=PropertyChanged}"
                         Height="30" 
                         Padding="8,5"
                         BorderBrush="#CCCCCC"
                         BorderThickness="1"
                         Margin="0,0,0,15"/>

                <!-- Phone Number -->
                <TextBlock Grid.Row="3" Grid.Column="0" Text="电话号码:" 
                           VerticalAlignment="Center" 
                           Margin="0,0,10,15" 
                           FontWeight="Medium"/>
                <TextBox Grid.Row="3" Grid.Column="1"
                         Text="{Binding PhoneNumber, UpdateSourceTrigger=PropertyChanged}"
                         Height="30"
                         Padding="8,5"
                         BorderBrush="#CCCCCC"
                         BorderThickness="1"
                         Margin="0,0,0,15"
                         ToolTip="当前电话号码，可以修改"/>

                <!-- Job Position -->
                <TextBlock Grid.Row="4" Grid.Column="0" Text="岗位职务:" 
                           VerticalAlignment="Center" 
                           Margin="0,0,10,15" 
                           FontWeight="Medium"/>
                <ComboBox Grid.Row="4" Grid.Column="1" 
                          ItemsSource="{Binding JobPositions}"
                          SelectedItem="{Binding SelectedJobPosition}"
                          Height="30" 
                          Padding="8,5"
                          BorderBrush="#CCCCCC"
                          BorderThickness="1"
                          Margin="0,0,0,15"/>

                <!-- Login Account -->
                <TextBlock Grid.Row="5" Grid.Column="0" Text="登录账号:"
                           VerticalAlignment="Center"
                           Margin="0,0,10,15"
                           FontWeight="Medium"/>
                <TextBox Grid.Row="5" Grid.Column="1"
                         Text="{Binding LoginAccount, UpdateSourceTrigger=PropertyChanged}"
                         Height="30"
                         Padding="8,5"
                         BorderBrush="#CCCCCC"
                         BorderThickness="1"
                         Margin="0,0,0,15"/>

                <!-- Password -->
                <TextBlock Grid.Row="6" Grid.Column="0" Text="密码:"
                           VerticalAlignment="Center"
                           Margin="0,0,10,15"
                           FontWeight="Medium"/>
                <TextBox Grid.Row="6" Grid.Column="1"
                         Text="{Binding Password, UpdateSourceTrigger=PropertyChanged}"
                         Height="30"
                         Padding="8,5"
                         BorderBrush="#CCCCCC"
                         BorderThickness="1"
                         Margin="0,0,0,15"
                         ToolTip="留空则不修改密码，输入新密码则更新"/>

                <!-- Loading Indicator -->
                <StackPanel Grid.Row="7" Grid.ColumnSpan="2"
                            Orientation="Horizontal" 
                            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Margin="0,10,0,0">
                    <ProgressBar IsIndeterminate="True" Width="20" Height="20" Margin="0,0,10,0"/>
                    <TextBlock Text="正在加载数据..." VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,20,0,0">
            <Button Content="保存" 
                    Command="{Binding SaveCommand}"
                    Style="{StaticResource PrimaryButtonStyle}"
                    Width="80" Height="35" 
                    Margin="0,0,10,0"/>
            <Button Content="取消" 
                    Command="{Binding CancelCommand}"
                    Style="{StaticResource SecondaryButtonStyle}"
                    Width="80" Height="35"/>
        </StackPanel>

        <!-- Saving Overlay -->
        <Border Grid.RowSpan="3" 
                Background="#80000000" 
                Visibility="{Binding IsSaving, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="正在保存..." Foreground="White" HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
