<Window x:Class="Sellsys.WpfClient.Views.Dialogs.CustomerServiceRecordsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="客服记录"
        Height="950"
        Width="1450"
        MinHeight="600" MinWidth="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <!-- 客户信息样式 -->
        <Style x:Key="CustomerInfoLabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <Style x:Key="CustomerInfoValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Margin" Value="0,0,20,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- 标题 -->
            <RowDefinition Height="Auto"/> <!-- 客户信息 -->
            <RowDefinition Height="*"/>    <!-- 记录列表 -->
            <RowDefinition Height="Auto"/> <!-- 底部按钮 -->
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0"
                   Text="客服记录"
                   FontSize="18"
                   FontWeight="Bold"
                   Margin="0,0,0,20"
                   HorizontalAlignment="Center"/>

        <!-- 客户信息 -->
        <Border Grid.Row="1"
                Background="{StaticResource TableHeaderBrush}"
                Padding="15,10"
                Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 客户单位信息 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="客户单位:" Style="{StaticResource CustomerInfoLabelStyle}"/>
                    <TextBlock Text="{Binding CustomerUnit}" Style="{StaticResource CustomerInfoValueStyle}"/>
                </StackPanel>

                <!-- 添加记录按钮 -->
                <Button Grid.Column="1"
                        Content="添加记录"
                        Command="{Binding AddRecordCommand}"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Width="100"
                        Height="35"
                        VerticalAlignment="Center"
                        Margin="15,0,0,0"/>
            </Grid>
        </Border>

        <!-- 记录列表 -->
        <Border Grid.Row="2"
                BorderBrush="{StaticResource BorderBrush}" 
                BorderThickness="1"
                Background="White">
            <Grid>
                <!-- Loading Indicator -->
                <Grid Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Grid.Background>
                        <SolidColorBrush Color="White" Opacity="0.7"/>
                    </Grid.Background>
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar IsIndeterminate="True" Width="200" Height="20" Margin="0,0,0,10"/>
                        <TextBlock Text="正在加载..." 
                                   Style="{StaticResource GlobalTextStyle}"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>

                <!-- Records Data Grid -->
                <DataGrid x:Name="RecordsDataGrid"
                          ItemsSource="{Binding Records}"
                          SelectedItem="{Binding SelectedRecord}"
                          Style="{StaticResource StandardDataGridStyle}"
                          ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                          RowStyle="{StaticResource DataGridRowStyle}"
                          AutoGenerateColumns="False">
                    <DataGrid.Columns>
                        <!-- 序号 -->
                        <DataGridTextColumn Header="序号"
                                            Width="60"
                                            IsReadOnly="True">
                            <DataGridTextColumn.Binding>
                                <Binding RelativeSource="{RelativeSource AncestorType=DataGridRow}"
                                         Converter="{StaticResource RowIndexConverter}"/>
                            </DataGridTextColumn.Binding>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        


                        <!-- 客户反馈 -->
                        <DataGridTextColumn Header="客户反馈"
                                            Binding="{Binding CustomerFeedback}"
                                            Width="1.5*" MinWidth="150"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                    <Setter Property="ToolTip" Value="{Binding CustomerFeedback}"/>
                                    <Setter Property="HorizontalAlignment" Value="Left"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 反馈回复 -->
                        <DataGridTextColumn Header="反馈回复"
                                            Binding="{Binding OurReply}"
                                            Width="1.5*" MinWidth="150"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                    <Setter Property="ToolTip" Value="{Binding OurReply}"/>
                                    <Setter Property="HorizontalAlignment" Value="Left"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 处理时间 -->
                        <DataGridTextColumn Header="处理时间"
                                            Binding="{Binding ProcessedDate, StringFormat=yyyy-MM-dd HH:mm:ss}"
                                            Width="140" MinWidth="140"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 客服 -->
                        <DataGridTextColumn Header="客服"
                                            Binding="{Binding SupportPersonDisplay}"
                                            Width="80" MinWidth="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 处理状态 -->
                        <DataGridTextColumn Header="处理状态"
                                            Binding="{Binding Status}"
                                            Width="80" MinWidth="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 创建时间 -->
                        <DataGridTextColumn Header="创建时间"
                                            Binding="{Binding CreatedAt, StringFormat=yyyy-MM-dd HH:mm:ss}"
                                            Width="140" MinWidth="140"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>



                        <!-- 操作 -->
                        <DataGridTemplateColumn Header="操作" Width="120" MinWidth="120" IsReadOnly="True">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="编辑" 
                                                Command="{Binding DataContext.EditRecordRowCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource EditButtonStyle}"
                                                Width="50"
                                                Height="25"
                                                FontSize="12"
                                                Margin="0,0,5,0"/>
                                        <Button Content="删除" 
                                                Command="{Binding DataContext.DeleteRecordRowCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource DeleteButtonStyle}"
                                                Width="50"
                                                Height="25"
                                                FontSize="12"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="3"
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,15,0,0">
            <Button Content="关闭" 
                    Command="{Binding CloseCommand}"
                    Style="{StaticResource DeleteButtonStyle}"
                    Width="80"
                    Height="35"/>
        </StackPanel>
    </Grid>
</Window>
