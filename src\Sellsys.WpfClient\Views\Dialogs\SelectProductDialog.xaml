<Window x:Class="Sellsys.WpfClient.Views.Dialogs.SelectProductDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="选择产品"
        Height="600"
        Width="1000"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#409EFF" Padding="15">
            <TextBlock Text="选择产品" 
                       Foreground="White" 
                       FontSize="18" 
                       FontWeight="Bold"/>
        </Border>

        <!-- Products Data Grid -->
        <DataGrid Grid.Row="1"
                  ItemsSource="{Binding Products}"
                  SelectedItem="{Binding SelectedProduct}"
                  Style="{StaticResource BlueHeaderDataGridStyle}"
                  Margin="10"
                  SelectionMode="Single"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  IsReadOnly="True">
            <DataGrid.Columns>
                <!-- 单选 -->
                <DataGridTemplateColumn Header="单选" Width="60">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <RadioButton IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                         GroupName="ProductSelection"
                                         HorizontalAlignment="Center"
                                         VerticalAlignment="Center"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <!-- 产品名称 -->
                <DataGridTextColumn Header="产品名称"
                                    Binding="{Binding Name}"
                                    Width="200"
                                    IsReadOnly="True">
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Setter Property="HorizontalAlignment" Value="Center"/>
                            <Setter Property="VerticalAlignment" Value="Center"/>
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>

                <!-- 型号规格 -->
                <DataGridTextColumn Header="型号规格"
                                    Binding="{Binding SpecificationDisplay}"
                                    Width="120"
                                    IsReadOnly="True">
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Setter Property="HorizontalAlignment" Value="Center"/>
                            <Setter Property="VerticalAlignment" Value="Center"/>
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>

                <!-- 计量单位 -->
                <DataGridTextColumn Header="计量单位"
                                    Binding="{Binding UnitDisplay}"
                                    Width="80"
                                    IsReadOnly="True">
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Setter Property="HorizontalAlignment" Value="Center"/>
                            <Setter Property="VerticalAlignment" Value="Center"/>
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>

                <!-- 产品定价 -->
                <DataGridTextColumn Header="产品定价"
                                    Binding="{Binding FormattedListPrice}"
                                    Width="100"
                                    IsReadOnly="True">
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Setter Property="HorizontalAlignment" Value="Center"/>
                            <Setter Property="VerticalAlignment" Value="Center"/>
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>

                <!-- 最低限价 -->
                <DataGridTextColumn Header="最低限价"
                                    Binding="{Binding FormattedMinPrice}"
                                    Width="100"
                                    IsReadOnly="True">
                    <DataGridTextColumn.ElementStyle>
                        <Style TargetType="TextBlock">
                            <Setter Property="HorizontalAlignment" Value="Center"/>
                            <Setter Property="VerticalAlignment" Value="Center"/>
                        </Style>
                    </DataGridTextColumn.ElementStyle>
                </DataGridTextColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Notice -->
        <Border Grid.Row="2" Background="#FFF3CD" BorderBrush="#FFEAA7" BorderThickness="1" Margin="10,0">
            <TextBlock Text="备注：产品只能单选" 
                       Foreground="#856404" 
                       FontSize="12" 
                       Padding="10,5"/>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="3" Padding="15" Background="#F8F9FA">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="取消"
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource GrayButtonStyle}"
                        Width="80"
                        Height="32"
                        Margin="0,0,10,0"/>
                <Button Content="确定"
                        Command="{Binding ConfirmCommand}"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Width="80"
                        Height="32"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
