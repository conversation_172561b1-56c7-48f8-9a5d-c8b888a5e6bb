﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Sellsys.Infrastructure.Data;

#nullable disable

namespace Sellsys.Infrastructure.Migrations
{
    [DbContext(typeof(SellsysDbContext))]
    [Migration("20250727062917_UpdateDecimalPrecision")]
    partial class UpdateDecimalPrecision
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.6");

            modelBuilder.Entity("Sellsys.Domain.Entities.AfterSalesRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ContactId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomerFeedback")
                        .HasColumnType("TEXT");

                    b.Property<int>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("OurReply")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ProcessedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int?>("SupportPersonId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ContactId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("SupportPersonId");

                    b.ToTable("AfterSalesRecords");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Contact", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Phone")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("Contacts");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Customer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("City")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("IndustryTypes")
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("Province")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Remarks")
                        .HasColumnType("TEXT");

                    b.Property<int?>("SalesPersonId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SupportPersonId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("Name");

                    b.HasIndex("SalesPersonId");

                    b.HasIndex("SupportPersonId");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Department", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Departments");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.DepartmentGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.ToTable("DepartmentGroups");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Employee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("BranchAccount")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("GroupId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("HashedPassword")
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("LoginUsername")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Phone")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int?>("RoleId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("GroupId");

                    b.HasIndex("LoginUsername")
                        .IsUnique();

                    b.HasIndex("RoleId");

                    b.ToTable("Employees");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Order", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("EffectiveDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("ManagerCommissionAmount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PaymentReceivedDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("SalesCommissionAmount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("SalesPersonId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("SupervisorCommissionAmount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("OrderNumber")
                        .IsUnique();

                    b.HasIndex("SalesPersonId");

                    b.HasIndex("Status");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.OrderItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("ActualPrice")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("OrderId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18, 2)");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.HasIndex("ProductId");

                    b.ToTable("OrderItems");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("ListPrice")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("ManagerCommission")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("MinPrice")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("SalesCommission")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("Specification")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("SupervisorCommission")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("Unit")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Products");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AccessibleModules")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Roles");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.SalesFollowUpLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ContactId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CustomerIntention")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("NextFollowUpDate")
                        .HasColumnType("TEXT");

                    b.Property<int?>("SalesPersonId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Summary")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ContactId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("SalesPersonId");

                    b.ToTable("SalesFollowUpLogs");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.AfterSalesRecord", b =>
                {
                    b.HasOne("Sellsys.Domain.Entities.Contact", "Contact")
                        .WithMany("AfterSalesRecords")
                        .HasForeignKey("ContactId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Sellsys.Domain.Entities.Customer", "Customer")
                        .WithMany("AfterSalesRecords")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sellsys.Domain.Entities.Employee", "SupportPerson")
                        .WithMany("AfterSalesRecords")
                        .HasForeignKey("SupportPersonId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Contact");

                    b.Navigation("Customer");

                    b.Navigation("SupportPerson");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Contact", b =>
                {
                    b.HasOne("Sellsys.Domain.Entities.Customer", "Customer")
                        .WithMany("Contacts")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Customer", b =>
                {
                    b.HasOne("Sellsys.Domain.Entities.Employee", "SalesPerson")
                        .WithMany("SalesCustomers")
                        .HasForeignKey("SalesPersonId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Sellsys.Domain.Entities.Employee", "SupportPerson")
                        .WithMany("SupportCustomers")
                        .HasForeignKey("SupportPersonId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("SalesPerson");

                    b.Navigation("SupportPerson");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.DepartmentGroup", b =>
                {
                    b.HasOne("Sellsys.Domain.Entities.Department", "Department")
                        .WithMany("Groups")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Employee", b =>
                {
                    b.HasOne("Sellsys.Domain.Entities.DepartmentGroup", "Group")
                        .WithMany("Employees")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Sellsys.Domain.Entities.Role", "Role")
                        .WithMany("Employees")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Group");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Order", b =>
                {
                    b.HasOne("Sellsys.Domain.Entities.Customer", "Customer")
                        .WithMany("Orders")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sellsys.Domain.Entities.Employee", "SalesPerson")
                        .WithMany("Orders")
                        .HasForeignKey("SalesPersonId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("SalesPerson");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.OrderItem", b =>
                {
                    b.HasOne("Sellsys.Domain.Entities.Order", "Order")
                        .WithMany("OrderItems")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sellsys.Domain.Entities.Product", "Product")
                        .WithMany("OrderItems")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Order");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.SalesFollowUpLog", b =>
                {
                    b.HasOne("Sellsys.Domain.Entities.Contact", "Contact")
                        .WithMany("SalesFollowUpLogs")
                        .HasForeignKey("ContactId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Sellsys.Domain.Entities.Customer", "Customer")
                        .WithMany("SalesFollowUpLogs")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sellsys.Domain.Entities.Employee", "SalesPerson")
                        .WithMany("SalesFollowUpLogs")
                        .HasForeignKey("SalesPersonId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Contact");

                    b.Navigation("Customer");

                    b.Navigation("SalesPerson");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Contact", b =>
                {
                    b.Navigation("AfterSalesRecords");

                    b.Navigation("SalesFollowUpLogs");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Customer", b =>
                {
                    b.Navigation("AfterSalesRecords");

                    b.Navigation("Contacts");

                    b.Navigation("Orders");

                    b.Navigation("SalesFollowUpLogs");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Department", b =>
                {
                    b.Navigation("Groups");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.DepartmentGroup", b =>
                {
                    b.Navigation("Employees");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Employee", b =>
                {
                    b.Navigation("AfterSalesRecords");

                    b.Navigation("Orders");

                    b.Navigation("SalesCustomers");

                    b.Navigation("SalesFollowUpLogs");

                    b.Navigation("SupportCustomers");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Order", b =>
                {
                    b.Navigation("OrderItems");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Product", b =>
                {
                    b.Navigation("OrderItems");
                });

            modelBuilder.Entity("Sellsys.Domain.Entities.Role", b =>
                {
                    b.Navigation("Employees");
                });
#pragma warning restore 612, 618
        }
    }
}
