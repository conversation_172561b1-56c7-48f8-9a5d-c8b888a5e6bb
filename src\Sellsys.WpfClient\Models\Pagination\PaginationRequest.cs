namespace Sellsys.WpfClient.Models.Pagination
{
    /// <summary>
    /// 分页请求模型
    /// </summary>
    public class PaginationRequest
    {
        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 13;

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string? SearchKeyword { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PaginationRequest()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">每页大小</param>
        public PaginationRequest(int pageNumber, int pageSize = 13)
        {
            PageNumber = pageNumber;
            PageSize = pageSize;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="searchKeyword">搜索关键词</param>
        public PaginationRequest(int pageNumber, int pageSize, string? searchKeyword)
        {
            PageNumber = pageNumber;
            PageSize = pageSize;
            SearchKeyword = searchKeyword;
        }
    }
}
