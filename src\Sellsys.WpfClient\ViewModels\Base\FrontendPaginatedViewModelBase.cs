using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Sellsys.WpfClient.Models.Pagination;

namespace Sellsys.WpfClient.ViewModels.Base
{
    /// <summary>
    /// 前端分页ViewModel基类
    /// 适用于后端API不支持分页的情况，在前端进行内存分页
    /// </summary>
    /// <typeparam name="T">数据项类型</typeparam>
    public abstract class FrontendPaginatedViewModelBase<T> : PaginatedViewModelBase<T>
    {
        #region Private Fields
        private List<T> _allData = new List<T>();
        private string _currentSearchKeyword = string.Empty;
        #endregion

        #region Protected Properties
        /// <summary>
        /// 所有数据（用于前端分页和搜索）
        /// </summary>
        protected List<T> AllData
        {
            get => _allData;
            set => _allData = value ?? new List<T>();
        }

        /// <summary>
        /// 当前搜索关键词
        /// </summary>
        protected string CurrentSearchKeyword
        {
            get => _currentSearchKeyword;
            set => _currentSearchKeyword = value ?? string.Empty;
        }
        #endregion

        #region Abstract Methods
        /// <summary>
        /// 从API加载所有数据的抽象方法，子类必须实现
        /// </summary>
        /// <returns>所有数据列表</returns>
        protected abstract Task<List<T>> LoadAllDataFromApiAsync();

        /// <summary>
        /// 搜索过滤数据的抽象方法，子类可重写
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <param name="searchKeyword">搜索关键词</param>
        /// <returns>过滤后的数据</returns>
        protected abstract List<T> FilterData(List<T> data, string searchKeyword);
        #endregion

        #region Override Methods
        /// <summary>
        /// 实现分页数据加载（重写基类方法）
        /// </summary>
        protected override async Task<PagedResult<T>> LoadPagedDataAsync(int pageNumber, int pageSize)
        {
            // 如果没有数据或需要刷新，从API加载
            if (!AllData.Any())
            {
                AllData = await LoadAllDataFromApiAsync();
            }

            // 应用搜索过滤
            var filteredData = string.IsNullOrWhiteSpace(CurrentSearchKeyword) 
                ? AllData 
                : FilterData(AllData, CurrentSearchKeyword);

            // 计算分页信息
            var totalCount = filteredData.Count;
            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
            
            // 获取当前页数据
            var pagedData = filteredData
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            return new PagedResult<T>(pagedData, totalCount, pageNumber, pageSize);
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// 刷新数据（重新从API加载）
        /// </summary>
        public override async Task RefreshDataAsync()
        {
            AllData.Clear(); // 清空缓存，强制重新加载
            await LoadDataAsync();
        }

        /// <summary>
        /// 搜索数据
        /// </summary>
        /// <param name="searchKeyword">搜索关键词</param>
        public virtual async Task SearchAsync(string searchKeyword)
        {
            CurrentSearchKeyword = searchKeyword;
            // 重置到第一页并重新加载数据
            await LoadPageAsync(1);
        }

        /// <summary>
        /// 清空搜索
        /// </summary>
        public virtual async Task ClearSearchAsync()
        {
            CurrentSearchKeyword = string.Empty;
            // 重置到第一页并重新加载数据
            await LoadPageAsync(1);
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// 加载指定页的数据
        /// </summary>
        private async Task LoadPageAsync(int pageNumber)
        {
            if (IsLoading) return;

            try
            {
                IsLoading = true;
                var result = await LoadPagedDataAsync(pageNumber, PageSize);

                CurrentPage = result.PageNumber;
                TotalPages = result.TotalPages;
                TotalCount = result.TotalCount;

                // 更新分页UI信息
                UpdatePageNumbers();
                UpdatePageInfo();

                // 通知子类更新数据
                OnDataLoaded(result.Items);
            }
            catch (Exception ex)
            {
                // 处理错误
                OnLoadError(ex);
            }
            finally
            {
                IsLoading = false;
            }
        }
        #endregion

        #region Virtual Methods
        /// <summary>
        /// 错误处理，子类可重写
        /// </summary>
        protected virtual void OnLoadError(Exception ex)
        {
            // 默认实现：输出调试信息
            System.Diagnostics.Debug.WriteLine($"FrontendPaginatedViewModelBase LoadError: {ex.Message}");
        }
        #endregion
    }
}
