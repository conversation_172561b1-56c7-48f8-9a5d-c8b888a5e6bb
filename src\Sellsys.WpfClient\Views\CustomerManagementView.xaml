<UserControl x:Class="Sellsys.WpfClient.Views.CustomerManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Sellsys.WpfClient.Views"
             xmlns:controls="clr-namespace:Sellsys.WpfClient.Controls"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Search Filters -->
            <RowDefinition Height="*"/>    <!-- Data Grid -->
            <RowDefinition Height="Auto"/> <!-- Pagination -->
        </Grid.RowDefinitions>

        <!-- Search and Filter Section -->
        <Border Grid.Row="0" Style="{StaticResource SearchAreaBorderStyle}">
            <WrapPanel Orientation="Horizontal">
                <!-- 客户名称 (输入框) -->
                <TextBlock Text="客户名称:" Style="{StaticResource SearchLabelStyle}"/>
                <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource StandardTextBoxStyle}"
                         Width="120" Height="32" Margin="0,0,15,0"/>

                <!-- 行业类别 (下拉框) -->
                <TextBlock Text="行业类别:" Style="{StaticResource SearchLabelStyle}"/>
                <ComboBox SelectedItem="{Binding SelectedIndustryType}"
                          ItemsSource="{Binding IndustryTypes}"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="100" Height="32" Margin="0,0,15,0"/>

                <!-- 省份 (下拉框) -->
                <TextBlock Text="省份:" Style="{StaticResource SearchLabelStyle}"/>
                <ComboBox SelectedItem="{Binding SelectedProvince}"
                          ItemsSource="{Binding Provinces}"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="80" Height="32" Margin="0,0,15,0"/>

                <!-- 城市 (下拉框) -->
                <TextBlock Text="城市:" Style="{StaticResource SearchLabelStyle}"/>
                <ComboBox SelectedItem="{Binding SelectedCity}"
                          ItemsSource="{Binding Cities}"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="80" Height="32" Margin="0,0,15,0"/>

                <!-- 联系状态 (下拉框) -->
                <TextBlock Text="联系状态:" Style="{StaticResource SearchLabelStyle}"/>
                <ComboBox SelectedItem="{Binding SelectedContactStatus}"
                          ItemsSource="{Binding ContactStatuses}"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="100" Height="32" Margin="0,0,15,0"/>

                <!-- 销售人员 (下拉框) -->
                <TextBlock Text="销售人员:" Style="{StaticResource SearchLabelStyle}"/>
                <ComboBox SelectedItem="{Binding SelectedResponsiblePerson}"
                          ItemsSource="{Binding ResponsiblePersons}"
                          Style="{StaticResource StandardComboBoxStyle}"
                          Width="100" Height="32" Margin="0,0,15,0"/>

                <!-- 查询和重置按钮 -->
                <Button Content="查询"
                        Command="{Binding SearchCommand}"
                        Style="{StaticResource PrimaryButtonStyle}"/>
                <Button Content="重置"
                        Command="{Binding ResetFiltersCommand}"
                        Style="{StaticResource GrayButtonStyle}"
                        Margin="0,0,15,0"/>

                <!-- 操作按钮 -->
                <Button Content="添加客户"
                        Command="{Binding AddCustomerCommand}"
                        Style="{StaticResource ActionButtonStyle}"
                        Margin="0,0,10,0"
                        Visibility="{Binding ., Converter={StaticResource OperationPermissionToVisibilityConverter}, ConverterParameter=客户管理:Create}"/>
                <Button Content="分配给销售"
                        Command="{Binding AssignSalesCommand}"
                        Style="{StaticResource ActionButtonStyle}"
                        Margin="0,0,10,0"
                        Visibility="{Binding ., Converter={StaticResource AssignPermissionToVisibilityConverter}, ConverterParameter=sales}"/>
                <Button Content="分配给客服"
                        Command="{Binding AssignSupportCommand}"
                        Style="{StaticResource ActionButtonStyle}"
                        Margin="0,0,0,0"
                        Visibility="{Binding ., Converter={StaticResource AssignPermissionToVisibilityConverter}, ConverterParameter=support}"/>
            </WrapPanel>
        </Border>

        <!-- Data Grid -->
        <Border Grid.Row="1"
                Background="White"
                BorderBrush="{StaticResource BorderBrush}"
                BorderThickness="0,0,0,0"
                CornerRadius="0">
            <Grid Margin="0" Background="White">
                <!-- Loading Indicator -->
                <Grid Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Grid.Background>
                        <SolidColorBrush Color="White" Opacity="0.7"/>
                    </Grid.Background>
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar IsIndeterminate="True" Width="200" Height="20" Margin="0,0,0,10"/>
                        <TextBlock Text="正在加载..."
                                   Style="{StaticResource GlobalTextStyle}"
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>

                <!-- Customer Data Grid -->
                <DataGrid x:Name="CustomersDataGrid"
                          ItemsSource="{Binding Customers}"
                          SelectedItem="{Binding SelectedCustomer}"
                          Style="{StaticResource ResponsiveBlueHeaderDataGridStyle}"
                          HorizontalAlignment="Stretch"
                          VerticalAlignment="Stretch"
                          Background="White"
                          BorderThickness="0"
                          ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                          ScrollViewer.VerticalScrollBarVisibility="Disabled"
                          ScrollViewer.CanContentScroll="False"
                          CanUserResizeRows="True">
                    <DataGrid.Columns>
                        <!-- 复选框列 -->
                        <DataGridTemplateColumn Width="40" MinWidth="40">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <CheckBox IsChecked="{Binding DataContext.IsAllSelected, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              Command="{Binding DataContext.SelectAllCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 行业类别 -->
                        <DataGridTextColumn Header="行业类别"
                                            Binding="{Binding IndustryType}"
                                            Width="*" MinWidth="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 省份 -->
                        <DataGridTextColumn Header="省份"
                                            Binding="{Binding Province}"
                                            Width="*" MinWidth="60"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 城市 -->
                        <DataGridTextColumn Header="城市"
                                            Binding="{Binding City}"
                                            Width="*" MinWidth="60"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 客户单位名称 -->
                        <DataGridTextColumn Header="客户单位名称"
                                            Binding="{Binding Name}"
                                            Width="3*" MinWidth="180"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Left"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="Margin" Value="5,2"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 联系人(蓝色数字) -->
                        <DataGridTemplateColumn Header="联系人" Width="60" MinWidth="60">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="{Binding ContactCount}"
                                            Command="{Binding DataContext.ViewContactsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Foreground="#409EFF"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Cursor="Hand"
                                            FontWeight="Bold"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Padding="0"
                                            Margin="0">
                                        <Button.Style>
                                            <Style TargetType="Button">
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="Button">
                                                            <ContentPresenter HorizontalAlignment="Center"
                                                                            VerticalAlignment="Center"/>
                                                            <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Foreground" Value="#66B3FF"/>
                                                                </Trigger>
                                                            </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 客户备注 -->
                        <DataGridTextColumn Header="客户备注"
                                            Binding="{Binding CustomerRemarks}"
                                            Width="3*" MinWidth="150"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Left"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="Margin" Value="5,2"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 客户状态 -->
                        <DataGridTextColumn Header="客户状态"
                                            Binding="{Binding CustomerStatus}"
                                            Width="*" MinWidth="70"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 销售 -->
                        <DataGridTextColumn Header="销售"
                                            Binding="{Binding SalesPersonDisplay}"
                                            Width="*" MinWidth="60"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 客服 -->
                        <DataGridTextColumn Header="客服"
                                            Binding="{Binding SupportPersonDisplay}"
                                            Width="*" MinWidth="60"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 创建时间 -->
                        <DataGridTextColumn Header="创建时间"
                                            Binding="{Binding CreatedAt, StringFormat=yyyy-MM-dd HH:mm:ss}"
                                            Width="140" MinWidth="140"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 操作列 -->
                        <DataGridTemplateColumn Header="操作" Width="120" MinWidth="120"
                                                Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=客户管理}">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="编辑"
                                                Command="{Binding DataContext.EditCustomerRowCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource BlueButtonStyle}"
                                                Width="50"
                                                Height="28"
                                                Margin="5,0,5,0"
                                                Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=客户管理}"/>
                                        <Button Content="删除"
                                                Command="{Binding DataContext.DeleteCustomerRowCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource DeleteButtonStyle}"
                                                Width="50"
                                                Height="28"
                                                Visibility="{Binding DataContext, RelativeSource={RelativeSource AncestorType=UserControl}, Converter={StaticResource PermissionToVisibilityConverter}, ConverterParameter=客户管理}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Pagination Control -->
        <Border Grid.Row="2"
                Background="White"
                BorderBrush="{StaticResource BorderBrush}"
                BorderThickness="0,0,0,0"
                Padding="10">
            <controls:PaginationControl DataContext="{Binding}"/>
        </Border>
    </Grid>
</UserControl>
