<Window x:Class="Sellsys.WpfClient.Views.Dialogs.EditPaymentInfoDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="编辑收款信息" 
        Height="500" 
        Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="White">
    
    <!-- 定义资源 -->
    <Window.Resources>
        <!-- 蓝色主题样式 -->
        <SolidColorBrush x:Key="PrimaryBlueBrush" Color="#409EFF"/>
        <SolidColorBrush x:Key="LightBlueBrush" Color="#ECF5FF"/>
        <SolidColorBrush x:Key="BorderBrush" Color="#DCDFE6"/>
        <SolidColorBrush x:Key="TextBrush" Color="#303133"/>
        <SolidColorBrush x:Key="SecondaryTextBrush" Color="#606266"/>
        
        <!-- 按钮样式 -->
        <Style x:Key="BlueButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBlueBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#66B1FF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3A8EE6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 灰色按钮样式 -->
        <Style x:Key="GrayButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#909399"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#A6A9AD"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#82848A"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 输入框样式 -->
        <Style x:Key="InputTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="36"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>
        
        <!-- 日期选择器样式 -->
        <Style x:Key="InputDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Height" Value="36"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>
    </Window.Resources>

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- 标题 -->
            <RowDefinition Height="20"/>   <!-- 间距 -->
            <RowDefinition Height="Auto"/> <!-- 订单信息 -->
            <RowDefinition Height="30"/>   <!-- 间距 -->
            <RowDefinition Height="Auto"/> <!-- 收款信息表单 -->
            <RowDefinition Height="*"/>    <!-- 弹性空间 -->
            <RowDefinition Height="Auto"/> <!-- 按钮 -->
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="编辑收款信息" 
                   FontSize="20" 
                   FontWeight="Bold"
                   Foreground="{StaticResource TextBrush}"
                   HorizontalAlignment="Center"/>

        <!-- 订单信息展示 -->
        <Border Grid.Row="2" 
                Background="{StaticResource LightBlueBrush}" 
                BorderBrush="{StaticResource BorderBrush}" 
                BorderThickness="1" 
                CornerRadius="6"
                Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="15"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 第一行 -->
                <StackPanel Grid.Row="0" Grid.Column="0">
                    <TextBlock Text="订单号:" FontWeight="Bold" Foreground="{StaticResource SecondaryTextBrush}" FontSize="12"/>
                    <TextBlock Text="{Binding OrderDetail.OrderNumber}" FontSize="14" Foreground="{StaticResource TextBrush}" Margin="0,2,0,0"/>
                </StackPanel>

                <StackPanel Grid.Row="0" Grid.Column="1">
                    <TextBlock Text="客户名称:" FontWeight="Bold" Foreground="{StaticResource SecondaryTextBrush}" FontSize="12"/>
                    <TextBlock Text="{Binding OrderDetail.CustomerName}" FontSize="14" Foreground="{StaticResource TextBrush}" Margin="0,2,0,0"/>
                </StackPanel>

                <StackPanel Grid.Row="0" Grid.Column="2">
                    <TextBlock Text="产品名称:" FontWeight="Bold" Foreground="{StaticResource SecondaryTextBrush}" FontSize="12"/>
                    <TextBlock Text="{Binding OrderDetail.ProductFullName}" FontSize="14" Foreground="{StaticResource TextBrush}" Margin="0,2,0,0"/>
                </StackPanel>

                <!-- 第二行 -->
                <StackPanel Grid.Row="2" Grid.Column="0">
                    <TextBlock Text="订单金额:" FontWeight="Bold" Foreground="{StaticResource SecondaryTextBrush}" FontSize="12"/>
                    <TextBlock Text="{Binding OrderDetail.FormattedTotalAmount}" FontSize="14" FontWeight="Bold" Foreground="{StaticResource PrimaryBlueBrush}" Margin="0,2,0,0"/>
                </StackPanel>

                <StackPanel Grid.Row="2" Grid.Column="1">
                    <TextBlock Text="已收金额:" FontWeight="Bold" Foreground="{StaticResource SecondaryTextBrush}" FontSize="12"/>
                    <TextBlock Text="{Binding OrderDetail.FormattedReceivedAmount}" FontSize="14" FontWeight="Bold" Foreground="#67C23A" Margin="0,2,0,0"/>
                </StackPanel>

                <StackPanel Grid.Row="2" Grid.Column="2">
                    <TextBlock Text="未收金额:" FontWeight="Bold" Foreground="{StaticResource SecondaryTextBrush}" FontSize="12"/>
                    <TextBlock Text="{Binding OrderDetail.FormattedUnreceivedAmount}" FontSize="14" FontWeight="Bold" Foreground="#E6A23C" Margin="0,2,0,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 收款信息表单 -->
        <StackPanel Grid.Row="4">
            <!-- 收款金额 -->
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           Text="收款金额:" 
                           VerticalAlignment="Center" 
                           FontWeight="Bold"
                           Foreground="{StaticResource TextBrush}"/>
                <TextBox Grid.Column="1" 
                         Text="{Binding ReceivedAmount, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource InputTextBoxStyle}"/>
            </Grid>

            <!-- 收款日期 -->
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           Text="收款日期:" 
                           VerticalAlignment="Center" 
                           FontWeight="Bold"
                           Foreground="{StaticResource TextBrush}"/>
                <DatePicker Grid.Column="1" 
                            SelectedDate="{Binding PaymentReceivedDate}"
                            Style="{StaticResource InputDatePickerStyle}"/>
            </Grid>

            <!-- 备注 -->
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           Text="备注:" 
                           VerticalAlignment="Top" 
                           FontWeight="Bold"
                           Foreground="{StaticResource TextBrush}"
                           Margin="0,8,0,0"/>
                <TextBox Grid.Column="1" 
                         Text="{Binding Remarks, UpdateSourceTrigger=PropertyChanged}"
                         Height="80"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         Padding="12,8"
                         BorderBrush="{StaticResource BorderBrush}"
                         BorderThickness="1"
                         Background="White"
                         FontSize="14"/>
            </Grid>
        </StackPanel>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="6" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <Button Content="取消" 
                    Command="{Binding CancelCommand}"
                    Style="{StaticResource GrayButtonStyle}"
                    Width="80"
                    Margin="0,0,15,0"/>
            <Button Content="确定" 
                    Command="{Binding ConfirmCommand}"
                    Style="{StaticResource BlueButtonStyle}"
                    Width="80"/>
        </StackPanel>
    </Grid>
</Window>
