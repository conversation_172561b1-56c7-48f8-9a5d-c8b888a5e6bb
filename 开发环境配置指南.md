# Sellsys 开发环境配置指南

## 🎯 环境说明

项目现在支持自动环境切换：
- **开发环境（Debug）**: 连接本地 `http://localhost:5078/api`
- **生产环境（Release）**: 连接云端 `http://***********:5000/api`

## 🔧 继续开发的步骤

### 1. 当前项目可以继续使用
✅ **推荐**：继续使用当前项目进行开发
- 代码没有被污染，只是配置了环境切换
- 便于版本管理和团队协作
- 云端部署是独立的，不影响开发

### 2. 开发时的工作流程

#### 日常开发
```bash
# 1. 启动本地 WebApi（开发环境）
cd src/Sellsys.WebApi
dotnet run

# 2. 启动客户端（Debug模式，自动连接本地）
# 在 Visual Studio 中按 F5 或者：
cd src/Sellsys.WpfClient
dotnet run
```

#### 测试生产环境
```bash
# 构建 Release 版本测试
dotnet build --configuration Release
cd src/Sellsys.WpfClient/bin/Release/net8.0-windows
./Sellsys.WpfClient.exe
```

### 3. 部署到生产环境

当需要更新云端版本时：
```bash
# 1. 打包新版本
dotnet publish src/Sellsys.WebApi/Sellsys.WebApi.csproj --configuration Release --runtime linux-x64 --self-contained true --output ./publish/webapi-new

# 2. 上传到服务器
scp -r publish/webapi-new/* root@***********:/opt/sellsys-new/

# 3. 在服务器上更新
ssh root@***********
sudo systemctl stop sellsys-webapi
sudo cp -r /opt/sellsys-new/* /opt/sellsys/
sudo systemctl start sellsys-webapi
```

## 🔄 环境切换机制

### 自动切换（推荐）
- **Debug 模式**：自动使用 `localhost:5078`
- **Release 模式**：自动使用 `***********:5000`

### 手动配置
如需手动指定环境，可以修改 `ApiService.cs` 中的配置：

```csharp
// 强制使用开发环境
BaseUrl = DevelopmentUrl;

// 强制使用生产环境  
BaseUrl = ProductionUrl;
```

## 📁 项目结构说明

```
项目根目录/
├── src/                    # 源代码（继续在这里开发）
├── publish/               # 打包输出目录
│   ├── webapi/           # 生产环境服务端（已部署）
│   ├── wpfclient-fixed/  # 生产环境客户端
│   └── webapi-new/       # 新版本打包目录
├── deploy/               # 部署脚本
└── 开发环境配置指南.md    # 本文档
```

## ⚠️ 注意事项

### 开发时
1. **本地数据库**：开发时使用本地的 `sellsys.db`
2. **端口冲突**：确保本地5078端口没有被占用
3. **CORS设置**：本地开发已配置允许跨域

### 部署时
1. **数据备份**：更新前备份云端数据库
2. **服务停止**：更新时需要停止服务
3. **权限设置**：确保文件权限正确

## 🚀 快速开始

### 新团队成员加入开发
1. 克隆项目：`git clone [项目地址]`
2. 还原依赖：`dotnet restore`
3. 启动后端：`cd src/Sellsys.WebApi && dotnet run`
4. 启动前端：在 Visual Studio 中打开并运行

### 现有开发者继续开发
1. 拉取最新代码：`git pull`
2. 继续正常开发流程
3. Debug模式自动连接本地，Release模式连接云端

## 🔍 故障排除

### 客户端连接失败
1. 检查后端是否启动：`http://localhost:5078/health`
2. 检查端口是否被占用：`netstat -an | findstr 5078`
3. 查看客户端日志：检查 Debug 输出

### 部署问题
1. 检查云端服务状态：`ssh root@*********** "sudo systemctl status sellsys-webapi"`
2. 查看云端日志：`ssh root@*********** "sudo journalctl -u sellsys-webapi -f"`

## 📞 技术支持

如有问题，请检查：
1. 本地开发环境是否正常
2. 网络连接是否正常
3. 服务器状态是否正常
