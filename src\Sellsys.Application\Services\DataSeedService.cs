using Microsoft.EntityFrameworkCore;
using Sellsys.Domain.Entities;
using Sellsys.Domain.Common;
using Sellsys.Infrastructure.Data;


namespace Sellsys.Application.Services
{
    /// <summary>
    /// 数据种子服务
    /// 负责在应用启动时创建基本的测试数据
    /// </summary>
    public class DataSeedService
    {
        private readonly SellsysDbContext _context;

        public DataSeedService(SellsysDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 初始化基本测试数据
        /// </summary>
        public async Task SeedDataAsync()
        {
            try
            {
                // 1. 创建部门
                await SeedDepartmentsAsync();
                
                // 2. 创建部门组
                await SeedDepartmentGroupsAsync();
                
                // 3. 创建员工
                await SeedEmployeesAsync();
                
                // 4. 创建产品
                await SeedProductsAsync();
                
                // 5. 创建客户
                await SeedCustomersAsync();

                Console.WriteLine("测试数据初始化完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试数据初始化失败: {ex.Message}");
                throw;
            }
        }

        private async Task SeedDepartmentsAsync()
        {
            if (!await _context.Departments.AnyAsync())
            {
                var departments = new[]
                {
                    new Department { Name = "销售部", CreatedAt = TimeHelper.GetBeijingTime() },
                    new Department { Name = "客服部", CreatedAt = TimeHelper.GetBeijingTime() },
                    new Department { Name = "技术部", CreatedAt = TimeHelper.GetBeijingTime() }
                };

                _context.Departments.AddRange(departments);
                await _context.SaveChangesAsync();
                Console.WriteLine("创建部门数据完成");
            }
        }

        private async Task SeedDepartmentGroupsAsync()
        {
            if (!await _context.DepartmentGroups.AnyAsync())
            {
                var salesDept = await _context.Departments.FirstAsync(d => d.Name == "销售部");
                var serviceDept = await _context.Departments.FirstAsync(d => d.Name == "客服部");

                var groups = new[]
                {
                    new DepartmentGroup { Name = "销售一组", DepartmentId = salesDept.Id, CreatedAt = TimeHelper.GetBeijingTime() },
                    new DepartmentGroup { Name = "销售二组", DepartmentId = salesDept.Id, CreatedAt = TimeHelper.GetBeijingTime() },
                    new DepartmentGroup { Name = "客服一组", DepartmentId = serviceDept.Id, CreatedAt = TimeHelper.GetBeijingTime() }
                };

                _context.DepartmentGroups.AddRange(groups);
                await _context.SaveChangesAsync();
                Console.WriteLine("创建部门组数据完成");
            }
        }

        private async Task SeedEmployeesAsync()
        {
            if (!await _context.Employees.AnyAsync())
            {
                var salesManagerRole = await _context.Roles.FirstAsync(r => r.Name == "销售经理");
                var salesRole = await _context.Roles.FirstAsync(r => r.Name == "销售");
                var serviceRole = await _context.Roles.FirstAsync(r => r.Name == "客服");

                var salesGroup1 = await _context.DepartmentGroups.FirstAsync(g => g.Name == "销售一组");
                var salesGroup2 = await _context.DepartmentGroups.FirstAsync(g => g.Name == "销售二组");
                var serviceGroup = await _context.DepartmentGroups.FirstAsync(g => g.Name == "客服一组");

                var employees = new[]
                {
                    new Employee 
                    { 
                        Name = "张经理", 
                        LoginUsername = "zhangmanager", 
                        HashedPassword = BCrypt.Net.BCrypt.HashPassword("123456"),
                        Phone = "13800138001",
                        GroupId = salesGroup1.Id,
                        RoleId = salesManagerRole.Id,
                        CreatedAt = TimeHelper.GetBeijingTime() 
                    },
                    new Employee 
                    { 
                        Name = "李销售", 
                        LoginUsername = "lisales", 
                        HashedPassword = BCrypt.Net.BCrypt.HashPassword("123456"),
                        Phone = "13800138002",
                        GroupId = salesGroup1.Id,
                        RoleId = salesRole.Id,
                        CreatedAt = TimeHelper.GetBeijingTime() 
                    },
                    new Employee 
                    { 
                        Name = "王销售", 
                        LoginUsername = "wangsales", 
                        HashedPassword = BCrypt.Net.BCrypt.HashPassword("123456"),
                        Phone = "13800138003",
                        GroupId = salesGroup2.Id,
                        RoleId = salesRole.Id,
                        CreatedAt = TimeHelper.GetBeijingTime() 
                    },
                    new Employee 
                    { 
                        Name = "赵客服", 
                        LoginUsername = "zhaoservice", 
                        HashedPassword = BCrypt.Net.BCrypt.HashPassword("123456"),
                        Phone = "13800138004",
                        GroupId = serviceGroup.Id,
                        RoleId = serviceRole.Id,
                        CreatedAt = TimeHelper.GetBeijingTime() 
                    }
                };

                _context.Employees.AddRange(employees);
                await _context.SaveChangesAsync();
                Console.WriteLine("创建员工数据完成");
            }
        }

        private async Task SeedProductsAsync()
        {
            if (!await _context.Products.AnyAsync())
            {
                var products = new[]
                {
                    new Product
                    {
                        Name = "企业管理软件",
                        Specification = "标准版",
                        Unit = "套",
                        ListPrice = 10000m,
                        MinPrice = 8000m,
                        SalesCommission = 500m,
                        SupervisorCommission = 200m,
                        ManagerCommission = 100m,
                        CreatedAt = TimeHelper.GetBeijingTime(),
                        UpdatedAt = TimeHelper.GetBeijingTime()
                    },
                    new Product
                    {
                        Name = "财务管理系统",
                        Specification = "专业版",
                        Unit = "套",
                        ListPrice = 15000m,
                        MinPrice = 12000m,
                        SalesCommission = 750m,
                        SupervisorCommission = 300m,
                        ManagerCommission = 150m,
                        CreatedAt = TimeHelper.GetBeijingTime(),
                        UpdatedAt = TimeHelper.GetBeijingTime()
                    },
                    new Product
                    {
                        Name = "客户关系管理",
                        Specification = "企业版",
                        Unit = "套",
                        ListPrice = 8000m,
                        MinPrice = 6000m,
                        SalesCommission = 400m,
                        SupervisorCommission = 160m,
                        ManagerCommission = 80m,
                        CreatedAt = TimeHelper.GetBeijingTime(),
                        UpdatedAt = TimeHelper.GetBeijingTime()
                    }
                };

                _context.Products.AddRange(products);
                await _context.SaveChangesAsync();
                Console.WriteLine("创建产品数据完成");
            }
        }

        private async Task SeedCustomersAsync()
        {
            if (!await _context.Customers.AnyAsync())
            {
                var salesPerson1 = await _context.Employees.FirstAsync(e => e.LoginUsername == "lisales");
                var salesPerson2 = await _context.Employees.FirstAsync(e => e.LoginUsername == "wangsales");
                var servicePerson = await _context.Employees.FirstAsync(e => e.LoginUsername == "zhaoservice");

                var customers = new[]
                {
                    new Customer
                    {
                        Name = "北京科技有限公司",
                        Province = "北京市",
                        City = "北京市",
                        Address = "朝阳区建国路88号",
                        Remarks = "重要客户，需要重点关注",
                        IndustryTypes = "科技,软件",
                        SalesPersonId = salesPerson1.Id,
                        SupportPersonId = servicePerson.Id,
                        CreatedAt = TimeHelper.GetBeijingTime()
                    },
                    new Customer
                    {
                        Name = "上海贸易集团",
                        Province = "上海市",
                        City = "上海市",
                        Address = "浦东新区陆家嘴金融中心",
                        Remarks = "有合作意向",
                        IndustryTypes = "贸易,进出口",
                        SalesPersonId = salesPerson2.Id,
                        SupportPersonId = servicePerson.Id,
                        CreatedAt = TimeHelper.GetBeijingTime()
                    },
                    new Customer
                    {
                        Name = "广州制造企业",
                        Province = "广东省",
                        City = "广州市",
                        Address = "天河区珠江新城",
                        Remarks = "制造业客户",
                        IndustryTypes = "制造业,机械",
                        SalesPersonId = salesPerson1.Id,
                        SupportPersonId = servicePerson.Id,
                        CreatedAt = TimeHelper.GetBeijingTime()
                    }
                };

                _context.Customers.AddRange(customers);
                await _context.SaveChangesAsync();

                // 为客户添加联系人
                await SeedContactsAsync();
                
                Console.WriteLine("创建客户数据完成");
            }
        }

        private async Task SeedContactsAsync()
        {
            var customers = await _context.Customers.ToListAsync();
            
            var contacts = new List<Contact>();
            
            foreach (var customer in customers)
            {
                contacts.Add(new Contact
                {
                    CustomerId = customer.Id,
                    Name = $"{customer.Name.Substring(0, 2)}总经理",
                    Phone = $"138{customer.Id:D8}",
                    IsPrimary = true,
                    CreatedAt = TimeHelper.GetBeijingTime()
                });
                
                contacts.Add(new Contact
                {
                    CustomerId = customer.Id,
                    Name = $"{customer.Name.Substring(0, 2)}采购",
                    Phone = $"139{customer.Id:D8}",
                    IsPrimary = false,
                    CreatedAt = TimeHelper.GetBeijingTime()
                });
            }

            _context.Contacts.AddRange(contacts);
            await _context.SaveChangesAsync();
            Console.WriteLine("创建联系人数据完成");
        }
    }
}
