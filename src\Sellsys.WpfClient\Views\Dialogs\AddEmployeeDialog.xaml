<Window x:Class="Sellsys.WpfClient.Views.Dialogs.AddEmployeeDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="添加员工"
        Height="600" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False">

    <Window.Resources>
        <!-- 对话框样式 -->
        <Style x:Key="DialogTitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="15,0"/>
        </Style>
        
        <Style x:Key="DialogLabelStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#303133"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>
        
        <Style x:Key="DialogTextBoxStyle" TargetType="TextBox">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="BorderBrush" Value="#DCDFE6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>
        
        <Style x:Key="DialogComboBoxStyle" TargetType="ComboBox">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="BorderBrush" Value="#DCDFE6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
        </Style>
        
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Background" Value="#409EFF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#66B1FF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3A8EE6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#606266"/>
            <Setter Property="BorderBrush" Value="#DCDFE6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="20,8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F5F7FA"/>
                                <Setter Property="BorderBrush" Value="#C6E2FF"/>
                                <Setter Property="Foreground" Value="#409EFF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/>  <!-- 标题栏 -->
            <RowDefinition Height="*"/>   <!-- 内容区域 -->
            <RowDefinition Height="60"/>  <!-- 按钮区域 -->
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#409EFF">
            <TextBlock Text="添加员工" Style="{StaticResource DialogTitleStyle}"/>
        </Border>

        <!-- 内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid Margin="30,30,30,20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="15"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="15"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="15"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="15"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="15"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="15"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="15"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 部门名称 -->
                <TextBlock Grid.Row="0" Grid.Column="0" 
                           Text="部门名称:" 
                           Style="{StaticResource DialogLabelStyle}"/>
                <ComboBox Grid.Row="0" Grid.Column="1" 
                          ItemsSource="{Binding Departments}"
                          SelectedItem="{Binding SelectedDepartment}"
                          DisplayMemberPath="Name"
                          Style="{StaticResource DialogComboBoxStyle}"/>

                <!-- 部门分组 -->
                <TextBlock Grid.Row="2" Grid.Column="0" 
                           Text="部门分组:" 
                           Style="{StaticResource DialogLabelStyle}"/>
                <ComboBox Grid.Row="2" Grid.Column="1" 
                          ItemsSource="{Binding DepartmentGroups}"
                          SelectedItem="{Binding SelectedDepartmentGroup}"
                          DisplayMemberPath="Name"
                          Style="{StaticResource DialogComboBoxStyle}"/>

                <!-- 员工姓名 -->
                <TextBlock Grid.Row="4" Grid.Column="0" 
                           Text="员工姓名:" 
                           Style="{StaticResource DialogLabelStyle}"/>
                <TextBox Grid.Row="4" Grid.Column="1" 
                         Text="{Binding EmployeeName, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource DialogTextBoxStyle}"/>

                <!-- 电话号码 -->
                <TextBlock Grid.Row="6" Grid.Column="0" 
                           Text="电话号码:" 
                           Style="{StaticResource DialogLabelStyle}"/>
                <TextBox Grid.Row="6" Grid.Column="1" 
                         Text="{Binding PhoneNumber, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource DialogTextBoxStyle}"/>

                <!-- 岗位职务 -->
                <TextBlock Grid.Row="8" Grid.Column="0" 
                           Text="岗位职务:" 
                           Style="{StaticResource DialogLabelStyle}"/>
                <ComboBox Grid.Row="8" Grid.Column="1" 
                          ItemsSource="{Binding Roles}"
                          SelectedItem="{Binding SelectedRole}"
                          DisplayMemberPath="Name"
                          Style="{StaticResource DialogComboBoxStyle}"/>

                <!-- 登录账号 -->
                <TextBlock Grid.Row="10" Grid.Column="0" 
                           Text="登录账号:" 
                           Style="{StaticResource DialogLabelStyle}"/>
                <TextBox Grid.Row="10" Grid.Column="1" 
                         Text="{Binding LoginUsername, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource DialogTextBoxStyle}"/>

                <!-- 初始密码 -->
                <TextBlock Grid.Row="12" Grid.Column="0" 
                           Text="初始密码:" 
                           Style="{StaticResource DialogLabelStyle}"/>
                <TextBox Grid.Row="12" Grid.Column="1"
                         Text="{Binding InitialPassword, UpdateSourceTrigger=PropertyChanged}"
                         Style="{StaticResource DialogTextBoxStyle}"
                         Background="#F5F7FA"/>
            </Grid>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <Border Grid.Row="2" Background="#F5F7FA" BorderBrush="#E4E7ED" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" 
                        HorizontalAlignment="Right" 
                        VerticalAlignment="Center" 
                        Margin="20,0">
                <Button Content="取消" 
                        Command="{Binding CancelCommand}"
                        Style="{StaticResource SecondaryButtonStyle}" 
                        Width="80" Height="35" 
                        Margin="0,0,10,0"/>
                <Button Content="保存" 
                        Command="{Binding SaveCommand}"
                        Style="{StaticResource PrimaryButtonStyle}" 
                        Width="80" Height="35"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
