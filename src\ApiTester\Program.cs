using System;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace ApiTester
{
    class Program
    {
        private static readonly HttpClient httpClient = new HttpClient();
        private const string BaseUrl = "http://localhost:5078/api";

        static async Task Main(string[] args)
        {
            Console.WriteLine("=== 销售管理系统 API 测试工具 ===");
            Console.WriteLine();

            // 测试API连接
            await TestApiConnection();
            
            // 测试登录
            await TestLogin();
            
            // 测试获取数据
            await TestGetData();

            Console.WriteLine();
            Console.WriteLine("测试完成！按任意键退出...");
            Console.ReadKey();
        }

        static async Task TestApiConnection()
        {
            Console.WriteLine("1. 测试API连接...");
            try
            {
                var response = await httpClient.GetAsync("http://localhost:5078/health");
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("   ✅ API服务连接成功");
                    Console.WriteLine($"   📍 API地址: http://localhost:5078");
                    Console.WriteLine($"   📍 Swagger文档: http://localhost:5078/swagger");
                }
                else
                {
                    Console.WriteLine($"   ❌ API连接失败: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ API连接异常: {ex.Message}");
                Console.WriteLine("   💡 请确保后端服务已启动");
            }
            Console.WriteLine();
        }

        static async Task TestLogin()
        {
            Console.WriteLine("2. 测试管理员登录...");
            try
            {
                var loginData = new
                {
                    Username = "admin",
                    Password = "admin"
                };

                var json = JsonSerializer.Serialize(loginData);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
                
                var response = await httpClient.PostAsync($"{BaseUrl}/auth/login", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine("   ✅ 管理员登录成功");
                    Console.WriteLine($"   📄 响应: {responseContent.Substring(0, Math.Min(100, responseContent.Length))}...");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"   ❌ 登录失败: {response.StatusCode}");
                    Console.WriteLine($"   📄 错误: {errorContent}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 登录异常: {ex.Message}");
            }
            Console.WriteLine();
        }

        static async Task TestGetData()
        {
            Console.WriteLine("3. 测试数据获取...");
            
            // 测试获取产品
            await TestEndpoint("products", "产品列表");
            
            // 测试获取客户
            await TestEndpoint("customers", "客户列表");
            
            // 测试获取员工
            await TestEndpoint("employees", "员工列表");
            
            // 测试获取角色
            await TestEndpoint("roles", "角色列表");
        }

        static async Task TestEndpoint(string endpoint, string description)
        {
            try
            {
                var response = await httpClient.GetAsync($"{BaseUrl}/{endpoint}");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"   ✅ {description}获取成功 (长度: {content.Length})");
                }
                else
                {
                    Console.WriteLine($"   ❌ {description}获取失败: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ {description}获取异常: {ex.Message}");
            }
        }
    }
}
