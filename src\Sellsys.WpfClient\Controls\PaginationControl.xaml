<UserControl x:Class="Sellsys.WpfClient.Controls.PaginationControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="50" d:DesignWidth="800">
    
    <UserControl.Resources>
        <!-- 分页按钮样式 -->
        <Style x:Key="PaginationButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="35"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#DDDDDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F0F8FF"/>
                                <Setter Property="BorderBrush" Value="#007ACC"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#E6F3FF"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#F5F5F5"/>
                                <Setter Property="Foreground" Value="#CCCCCC"/>
                                <Setter Property="BorderBrush" Value="#EEEEEE"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 当前页按钮样式 -->
        <Style x:Key="CurrentPageButtonStyle" TargetType="Button" BasedOn="{StaticResource PaginationButtonStyle}">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#007ACC"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>

        <!-- 导航按钮样式 -->
        <Style x:Key="NavigationButtonStyle" TargetType="Button" BasedOn="{StaticResource PaginationButtonStyle}">
            <Setter Property="Width" Value="80"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>


    </UserControl.Resources>

    <Border Background="White"
            BorderBrush="#EEEEEE"
            BorderThickness="0,1,0,0"
            Padding="15,10">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：分页按钮 -->
            <StackPanel Grid.Column="0"
                        Orientation="Horizontal"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Margin="0,3,0,0">
                
                <!-- 上一页按钮 -->
                <Button Content="← 上一页"
                        Command="{Binding PreviousPageCommand}"
                        Style="{StaticResource NavigationButtonStyle}"
                        IsEnabled="{Binding HasPreviousPage}"/>

                <!-- 页码按钮 -->
                <ItemsControl ItemsSource="{Binding PageNumbers}">
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <StackPanel Orientation="Horizontal"/>
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Button Content="{Binding}"
                                    Command="{Binding DataContext.GoToPageCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                    CommandParameter="{Binding}"
                                    Style="{StaticResource PaginationButtonStyle}"/>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>

                <!-- 下一页按钮 -->
                <Button Content="下一页 →"
                        Command="{Binding NextPageCommand}"
                        Style="{StaticResource NavigationButtonStyle}"
                        IsEnabled="{Binding HasNextPage}"/>
            </StackPanel>

            <!-- 中间：页面信息 -->
            <TextBlock Grid.Column="1"
                       Text="{Binding PageInfo}"
                       VerticalAlignment="Center"
                       HorizontalAlignment="Center"
                       Margin="20,0"
                       FontSize="12"
                       Foreground="#666666"/>


        </Grid>
    </Border>
</UserControl>
