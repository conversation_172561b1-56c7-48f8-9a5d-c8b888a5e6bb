<Window x:Class="Sellsys.WpfClient.Views.Dialogs.ViewContactsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="查看联系人" 
        Height="600" Width="800"
        MinHeight="500" MinWidth="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        ShowInTaskbar="False">
    
    <Window.Resources>
        <!-- 弹窗样式 -->
        <Style x:Key="DialogTitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="15,0"/>
        </Style>
        
        <Style x:Key="DialogLabelStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#303133"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>
        
        <Style x:Key="InfoTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#303133"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/> <!-- Title Bar -->
            <RowDefinition Height="*"/>  <!-- Content -->
            <RowDefinition Height="60"/> <!-- Buttons -->
        </Grid.RowDefinitions>

        <!-- Title Bar -->
        <Border Grid.Row="0" Background="{StaticResource PopupTitleBrush}">
            <TextBlock Text="查看联系人" Style="{StaticResource DialogTitleStyle}"/>
        </Border>

        <!-- Content Area -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30">
            <StackPanel>
                <!-- 客户单位 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="客户单位：" Style="{StaticResource DialogLabelStyle}"/>
                    <TextBlock Grid.Column="1" 
                               Text="{Binding CustomerName}" 
                               Style="{StaticResource InfoTextStyle}"/>
                </Grid>

                <!-- 详细地址 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="详细地址：" Style="{StaticResource DialogLabelStyle}"/>
                    <TextBlock Grid.Column="1" 
                               Text="{Binding DetailedAddress}" 
                               Style="{StaticResource InfoTextStyle}"/>
                </Grid>

                <!-- 客户备注 -->
                <Grid Margin="0,0,0,25">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="客户备注：" Style="{StaticResource DialogLabelStyle}" VerticalAlignment="Top"/>
                    <Border Grid.Column="1" 
                            BorderBrush="{StaticResource BorderBrush}" 
                            BorderThickness="1" 
                            Background="White"
                            MinHeight="80"
                            Padding="10">
                        <TextBlock Text="{Binding CustomerRemarks}" 
                                   Style="{StaticResource InfoTextStyle}"
                                   VerticalAlignment="Top"/>
                    </Border>
                </Grid>

                <!-- 联系人列表 -->
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="联系人列表：" Style="{StaticResource DialogLabelStyle}" Margin="0,0,0,15"/>
                    
                    <Border Grid.Row="1" BorderBrush="{StaticResource BorderBrush}" BorderThickness="1" Background="White">
                        <ItemsControl ItemsSource="{Binding Contacts}" Margin="15">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="80"/>
                                            <ColumnDefinition Width="150"/>
                                            <ColumnDefinition Width="80"/>
                                            <ColumnDefinition Width="150"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="联系人：" Style="{StaticResource DialogLabelStyle}"/>
                                        <Border Grid.Column="1" 
                                                BorderBrush="{StaticResource BorderBrush}" 
                                                BorderThickness="1" 
                                                Background="White"
                                                Height="32"
                                                Padding="8,6">
                                            <TextBlock Text="{Binding Name}" 
                                                       Style="{StaticResource InfoTextStyle}"
                                                       VerticalAlignment="Center"/>
                                        </Border>
                                        
                                        <TextBlock Grid.Column="2" Text="电话：" Style="{StaticResource DialogLabelStyle}" Margin="15,0,5,0"/>
                                        <Border Grid.Column="3" 
                                                BorderBrush="{StaticResource BorderBrush}" 
                                                BorderThickness="1" 
                                                Background="White"
                                                Height="32"
                                                Padding="8,6">
                                            <TextBlock Text="{Binding Phone}" 
                                                       Style="{StaticResource InfoTextStyle}"
                                                       VerticalAlignment="Center"/>
                                        </Border>
                                        
                                        <StackPanel Grid.Column="4" Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0,0,0">
                                            <CheckBox IsChecked="{Binding IsPrimary}"
                                                      Content="关键人"
                                                      VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Border>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- Button Area -->
        <Border Grid.Row="2" Background="#F8F9FA" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,1,0,0">
            <Button Content="关闭"
                    Command="{Binding CloseCommand}"
                    Style="{StaticResource DeleteButtonStyle}"
                    Width="80"
                    Height="35"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Margin="20"/>
        </Border>
    </Grid>
</Window>
