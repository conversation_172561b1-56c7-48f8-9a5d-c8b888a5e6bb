<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 全局颜色定义 -->
    <SolidColorBrush x:Key="TopBarBackgroundBrush" Color="#007ACC"/>
    <SolidColorBrush x:Key="LeftMenuBackgroundBrush" Color="#C5D9EF"/>
    <SolidColorBrush x:Key="PrimaryButtonBrush" Color="#409EFF"/>
    <SolidColorBrush x:Key="EditButtonBrush" Color="#E6A23C"/>
    <SolidColorBrush x:Key="DeleteButtonBrush" Color="#909399"/>
    <SolidColorBrush x:Key="PopupTitleBrush" Color="#409EFF"/>
    <SolidColorBrush x:Key="TableHeaderBrush" Color="#F5F7FA"/>
    <SolidColorBrush x:Key="PrimaryTextBrush" Color="#303133"/>
    <SolidColorBrush x:Key="SecondaryTextBrush" Color="#606266"/>
    <SolidColorBrush x:Key="HoverBackgroundBrush" Color="#F5F7FA"/>
    <SolidColorBrush x:Key="BorderBrush" Color="#DCDFE6"/>
    <SolidColorBrush x:Key="SelectedRowBackgroundBrush" Color="#E3F2FD"/>

    <!-- 全局字体样式 -->
    <Style x:Key="GlobalTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <Style x:Key="TitleTextStyle" TargetType="TextBlock" BasedOn="{StaticResource GlobalTextStyle}">
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="ModuleTitleStyle" TargetType="TextBlock" BasedOn="{StaticResource GlobalTextStyle}">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="Bold"/>
    </Style>

    <Style x:Key="MenuTextStyle" TargetType="TextBlock" BasedOn="{StaticResource GlobalTextStyle}">
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- 对话框标签样式 - 与左侧菜单保持一致 -->
    <Style x:Key="DialogLabelStyle" TargetType="TextBlock" BasedOn="{StaticResource GlobalTextStyle}">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="FontWeight" Value="Normal"/>
    </Style>

    <!-- 对话框RadioButton样式 - 与左侧菜单保持一致 -->
    <Style x:Key="DialogRadioButtonStyle" TargetType="RadioButton">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
    </Style>

    <!-- 统一的搜索区域样式 -->
    <Style x:Key="SearchAreaBorderStyle" TargetType="Border">
        <Setter Property="Background" Value="#F8F9FA"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="CornerRadius" Value="0"/>
        <Setter Property="Padding" Value="15,15,15,15"/>
        <Setter Property="Margin" Value="0,0,0,0"/>
    </Style>

    <!-- 统一的搜索条件标签样式 -->
    <Style x:Key="SearchLabelStyle" TargetType="TextBlock" BasedOn="{StaticResource GlobalTextStyle}">
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,0,5,0"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- 统一的搜索控件样式 -->
    <Style x:Key="SearchControlStyle" TargetType="FrameworkElement">
        <Setter Property="Height" Value="32"/>
        <Setter Property="Margin" Value="0,0,15,0"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 按钮样式 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryButtonBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Width" Value="60"/>
        <Setter Property="Margin" Value="0,0,10,0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            BorderBrush="{StaticResource BorderBrush}"
                            BorderThickness="1">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#3A8EEF"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.5"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 灰色按钮样式 (重置按钮) -->
    <Style x:Key="GrayButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="#909399"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Width" Value="60"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            BorderBrush="{StaticResource BorderBrush}"
                            BorderThickness="1">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#808389"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 操作按钮样式 (添加客户等) -->
    <Style x:Key="ActionButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Width" Value="100"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Margin" Value="0,0,10,0"/>
    </Style>

    <Style x:Key="EditButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="{StaticResource EditButtonBrush}"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Margin" Value="0,0,10,0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            BorderBrush="{StaticResource BorderBrush}"
                            BorderThickness="1">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#D6922C"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="DeleteButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="{StaticResource DeleteButtonBrush}"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="4" 
                            BorderBrush="{StaticResource BorderBrush}" 
                            BorderThickness="1">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#808389"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 绿色按钮样式 (客服相关) -->
    <Style x:Key="SupportButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
        <Setter Property="Background" Value="#27AE60"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            BorderBrush="#229954"
                            BorderThickness="1">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#229954"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#1E8449"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>



    <!-- 蓝色按钮样式 (编辑等) -->
    <Style x:Key="BlueButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="#3498DB"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            BorderBrush="#2980B9"
                            BorderThickness="1">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#2980B9"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#21618C"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 导航按钮样式 -->
    <Style x:Key="NavigationButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Margin" Value="8,5"/>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            Padding="10,0">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource HoverBackgroundBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 选中的导航按钮样式 -->
    <Style x:Key="SelectedNavigationButtonStyle" TargetType="Button" BasedOn="{StaticResource NavigationButtonStyle}">
        <Setter Property="Background" Value="{StaticResource PrimaryButtonBrush}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="#F5F7FA"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#E6E8EB"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 输入框样式 -->
    <Style x:Key="StandardTextBoxStyle" TargetType="TextBox">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryButtonBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 多行文本框样式 -->
    <Style x:Key="MultiLineTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource StandardTextBoxStyle}">
        <Setter Property="VerticalContentAlignment" Value="Top"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="Height" Value="Auto"/>
        <Setter Property="MinHeight" Value="80"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="AcceptsReturn" Value="True"/>
        <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
    </Style>

    <!-- DataGrid样式 -->
    <!-- DataGrid列标题样式 - 蓝色主题 -->
    <Style x:Key="DataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
        <Setter Property="Background" Value="#007ACC"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="BorderBrush" Value="#005A9B"/>
        <Setter Property="BorderThickness" Value="0,0,1,1"/>
    </Style>

    <!-- DataGrid行样式 -->
    <Style x:Key="DataGridRowStyle" TargetType="DataGridRow">
        <Setter Property="Background" Value="White"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource HoverBackgroundBrush}"/>
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="{StaticResource SelectedRowBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- DataGrid单元格样式 -->
    <Style x:Key="DataGridCellStyle" TargetType="DataGridCell">
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="VerticalAlignment" Value="Stretch"/>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Style.Triggers>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="StandardDataGridStyle" TargetType="DataGrid">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HorizontalGridLinesBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="RowHeight" Value="60"/>
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="CanUserReorderColumns" Value="False"/>
        <Setter Property="CanUserResizeRows" Value="False"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="AlternatingRowBackground" Value="Transparent"/>
        <Setter Property="RowStyle" Value="{StaticResource DataGridRowStyle}"/>
        <Setter Property="CellStyle" Value="{StaticResource DataGridCellStyle}"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="RowHeaderWidth" Value="0"/>
    </Style>

    <!-- 蓝色表头DataGrid样式 -->
    <Style x:Key="BlueHeaderDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource StandardDataGridStyle}">
        <Setter Property="ColumnHeaderStyle" Value="{StaticResource DataGridColumnHeaderStyle}"/>
        <Setter Property="RowStyle" Value="{StaticResource DataGridRowStyle}"/>
    </Style>

    <!-- 响应式DataGrid样式 -->
    <Style x:Key="ResponsiveDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource StandardDataGridStyle}">
        <Setter Property="ColumnHeaderStyle" Value="{StaticResource DataGridColumnHeaderStyle}"/>
        <Setter Property="RowStyle" Value="{StaticResource DataGridRowStyle}"/>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="VerticalAlignment" Value="Stretch"/>
        <Setter Property="CanUserResizeColumns" Value="True"/>
        <Setter Property="ColumnWidth" Value="*"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Disabled"/>
        <Setter Property="ScrollViewer.CanContentScroll" Value="False"/>
    </Style>

    <!-- 响应式蓝色表头DataGrid样式 -->
    <Style x:Key="ResponsiveBlueHeaderDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource ResponsiveDataGridStyle}">
        <Setter Property="ColumnHeaderStyle" Value="{StaticResource DataGridColumnHeaderStyle}"/>
        <Setter Property="RowStyle" Value="{StaticResource DataGridRowStyle}"/>
    </Style>

    <!-- ComboBox样式 -->
    <Style x:Key="StandardComboBoxStyle" TargetType="ComboBox">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Style.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryButtonBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 链接按钮样式 -->
    <Style x:Key="LinkButtonStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <ContentPresenter HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="TextBlock.TextDecorations" Value="Underline"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- DatePicker样式 -->
    <Style x:Key="StandardDatePickerStyle" TargetType="DatePicker">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Padding" Value="5"/>
    </Style>



</ResourceDictionary>
