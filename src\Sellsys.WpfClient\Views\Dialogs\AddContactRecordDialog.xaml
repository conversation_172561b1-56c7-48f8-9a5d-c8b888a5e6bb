<Window x:Class="Sellsys.WpfClient.Views.Dialogs.AddContactRecordDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="联系记录"
        Height="750"
        Width="1100"
        MinHeight="600" MinWidth="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#409EFF" Padding="15">
            <TextBlock Text="联系记录" 
                       Foreground="White" 
                       FontSize="18" 
                       FontWeight="Bold"/>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Disabled" Padding="20">
            <StackPanel>
                <!-- 客户单位 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="客户单位：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                    <Border Grid.Column="1" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="1" Padding="8">
                        <TextBlock Text="{Binding CustomerName}" VerticalAlignment="Center"/>
                    </Border>
                </Grid>

                <!-- 联系人 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="联系人：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                    <ComboBox Grid.Column="1"
                              ItemsSource="{Binding Contacts}"
                              SelectedItem="{Binding SelectedContact}"
                              DisplayMemberPath="Name"
                              Style="{StaticResource StandardComboBoxStyle}"
                              Height="32"/>
                </Grid>

                <!-- 联系电话 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="联系电话：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                    <TextBox Grid.Column="1"
                             Text="{Binding ContactPhone}"
                             Style="{StaticResource StandardTextBoxStyle}"
                             IsReadOnly="True"
                             Background="#F5F5F5"
                             Height="32"/>
                </Grid>

                <!-- 联系记录 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="联系记录：" VerticalAlignment="Top" Style="{StaticResource DialogLabelStyle}" Margin="0,8,0,0"/>
                    <TextBox Grid.Column="1"
                             Text="{Binding ContactSummary}"
                             Style="{StaticResource MultiLineTextBoxStyle}"
                             Height="80"/>
                </Grid>

                <!-- 联系时间 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="联系时间：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                    <Border Grid.Column="1" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="1" Padding="8">
                        <TextBlock Text="{Binding CurrentDateTime}" VerticalAlignment="Center"/>
                    </Border>
                </Grid>

                <!-- 下次预约 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="下次预约：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                    <DatePicker Grid.Column="1"
                                SelectedDate="{Binding NextFollowUpDate}"
                                Style="{StaticResource StandardDatePickerStyle}"
                                Height="32"
                                Margin="0,0,10,0"/>
                    <Button Grid.Column="2"
                            Content="取消预约"
                            Command="{Binding CancelAppointmentCommand}"
                            Style="{StaticResource GrayButtonStyle}"
                            Width="80"
                            Height="32"/>
                </Grid>

                <!-- 客户状态 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="客户状态：" VerticalAlignment="Top" Style="{StaticResource DialogLabelStyle}" Margin="0,8,0,0"/>
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <RadioButton Content="待联系"
                                     IsChecked="{Binding IsWaitingContact}"
                                     GroupName="CustomerStatus"
                                     Style="{StaticResource DialogRadioButtonStyle}"
                                     Margin="0,0,20,0"
                                     VerticalAlignment="Center"/>
                        <RadioButton Content="跟进中"
                                     IsChecked="{Binding IsFollowingUp}"
                                     GroupName="CustomerStatus"
                                     Style="{StaticResource DialogRadioButtonStyle}"
                                     Margin="0,0,20,0"
                                     VerticalAlignment="Center"/>
                        <RadioButton Content="已成交"
                                     IsChecked="{Binding IsCompleted}"
                                     GroupName="CustomerStatus"
                                     Style="{StaticResource DialogRadioButtonStyle}"
                                     VerticalAlignment="Center"/>
                    </StackPanel>
                </Grid>

                <!-- 客户意向 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="客户意向：" VerticalAlignment="Top" Style="{StaticResource DialogLabelStyle}" Margin="0,8,0,0"/>
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <RadioButton Content="高"
                                     IsChecked="{Binding IsIntentionHigh}"
                                     GroupName="CustomerIntention"
                                     Style="{StaticResource DialogRadioButtonStyle}"
                                     Margin="0,0,15,0"
                                     VerticalAlignment="Center"/>
                        <RadioButton Content="中"
                                     IsChecked="{Binding IsIntentionMedium}"
                                     GroupName="CustomerIntention"
                                     Style="{StaticResource DialogRadioButtonStyle}"
                                     Margin="0,0,15,0"
                                     VerticalAlignment="Center"/>
                        <RadioButton Content="低"
                                     IsChecked="{Binding IsIntentionLow}"
                                     GroupName="CustomerIntention"
                                     Style="{StaticResource DialogRadioButtonStyle}"
                                     VerticalAlignment="Center"/>
                    </StackPanel>
                </Grid>

                <!-- 销售 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="销售：" VerticalAlignment="Center" Style="{StaticResource DialogLabelStyle}"/>
                    <Border Grid.Column="1" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="1" Padding="8">
                        <TextBlock Text="{Binding SalesPersonName}" VerticalAlignment="Center"/>
                    </Border>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Padding="15" Background="#F8F9FA">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="关闭"
                        Command="{Binding CloseCommand}"
                        Style="{StaticResource GrayButtonStyle}"
                        Width="80"
                        Height="32"
                        Margin="0,0,10,0"/>
                <Button Content="保存"
                        Command="{Binding SaveCommand}"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Width="80"
                        Height="32"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
