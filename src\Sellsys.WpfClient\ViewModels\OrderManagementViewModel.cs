using Sellsys.WpfClient.Commands;
using Sellsys.WpfClient.Models;
using Sellsys.WpfClient.Services;
using Sellsys.WpfClient.ViewModels.Base;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows;
using System.Linq;

namespace Sellsys.WpfClient.ViewModels
{
    public class OrderManagementViewModel : FrontendPaginatedViewModelBase<Order>
    {
        private readonly ApiService _apiService;
        private ObservableCollection<Order> _orders;
        private ObservableCollection<Customer> _customers;
        private ObservableCollection<Employee> _employees;
        private ObservableCollection<Product> _products;
        private Order? _selectedOrder;
        private OrderSummary? _orderSummary;


        // 搜索和筛选属性
        private string _searchCustomerName = string.Empty;
        private string _searchProductName = string.Empty;
        private DateTime? _effectiveDateFrom;
        private DateTime? _effectiveDateTo;
        private DateTime? _expiryDateFrom;
        private DateTime? _expiryDateTo;
        private DateTime? _createdDateFrom;
        private DateTime? _createdDateTo;
        private string? _selectedStatus;
        private int? _selectedSalesPersonId;

        // 筛选选项
        private ObservableCollection<string> _statusOptions;
        private ObservableCollection<Employee> _salesPersonOptions;
        private ObservableCollection<string> _productOptions;

        // 选中的筛选项
        private string? _selectedProduct;
        private DateTime? _selectedEffectiveDateFilter;
        private DateTime? _selectedExpiryDateFilter;
        private DateTime? _selectedCreatedDateFilter;

        #region Properties

        public ObservableCollection<Order> Orders
        {
            get => _orders;
            set => SetProperty(ref _orders, value);
        }

        public ObservableCollection<Customer> Customers
        {
            get => _customers;
            set => SetProperty(ref _customers, value);
        }

        public ObservableCollection<Employee> Employees
        {
            get => _employees;
            set => SetProperty(ref _employees, value);
        }

        public ObservableCollection<Product> Products
        {
            get => _products;
            set => SetProperty(ref _products, value);
        }

        public Order? SelectedOrder
        {
            get => _selectedOrder;
            set => SetProperty(ref _selectedOrder, value);
        }

        public OrderSummary? OrderSummary
        {
            get => _orderSummary;
            set => SetProperty(ref _orderSummary, value);
        }

        // 搜索和筛选属性
        public string SearchCustomerName
        {
            get => _searchCustomerName;
            set => SetProperty(ref _searchCustomerName, value);
        }

        public string SearchProductName
        {
            get => _searchProductName;
            set => SetProperty(ref _searchProductName, value);
        }

        public DateTime? EffectiveDateFrom
        {
            get => _effectiveDateFrom;
            set => SetProperty(ref _effectiveDateFrom, value);
        }

        public DateTime? EffectiveDateTo
        {
            get => _effectiveDateTo;
            set => SetProperty(ref _effectiveDateTo, value);
        }

        public DateTime? ExpiryDateFrom
        {
            get => _expiryDateFrom;
            set => SetProperty(ref _expiryDateFrom, value);
        }

        public DateTime? ExpiryDateTo
        {
            get => _expiryDateTo;
            set => SetProperty(ref _expiryDateTo, value);
        }

        public DateTime? CreatedDateFrom
        {
            get => _createdDateFrom;
            set => SetProperty(ref _createdDateFrom, value);
        }

        public DateTime? CreatedDateTo
        {
            get => _createdDateTo;
            set => SetProperty(ref _createdDateTo, value);
        }

        public string? SelectedStatus
        {
            get => _selectedStatus;
            set => SetProperty(ref _selectedStatus, value);
        }

        public int? SelectedSalesPersonId
        {
            get => _selectedSalesPersonId;
            set => SetProperty(ref _selectedSalesPersonId, value);
        }

        // 筛选选项
        public ObservableCollection<string> StatusOptions
        {
            get => _statusOptions;
            set => SetProperty(ref _statusOptions, value);
        }

        public ObservableCollection<Employee> SalesPersonOptions
        {
            get => _salesPersonOptions;
            set => SetProperty(ref _salesPersonOptions, value);
        }

        public ObservableCollection<string> ProductOptions
        {
            get => _productOptions;
            set => SetProperty(ref _productOptions, value);
        }

        public string? SelectedProduct
        {
            get => _selectedProduct;
            set => SetProperty(ref _selectedProduct, value);
        }

        public DateTime? SelectedEffectiveDateFilter
        {
            get => _selectedEffectiveDateFilter;
            set
            {
                SetProperty(ref _selectedEffectiveDateFilter, value);
                // 当选择日期时，设置筛选范围为从该日期开始
                EffectiveDateFrom = value;
                EffectiveDateTo = null; // 不设置结束日期，表示从该日期开始的所有记录
            }
        }

        public DateTime? SelectedExpiryDateFilter
        {
            get => _selectedExpiryDateFilter;
            set
            {
                SetProperty(ref _selectedExpiryDateFilter, value);
                // 当选择日期时，设置筛选范围为从该日期开始
                ExpiryDateFrom = value;
                ExpiryDateTo = null; // 不设置结束日期，表示从该日期开始的所有记录
            }
        }

        public DateTime? SelectedCreatedDateFilter
        {
            get => _selectedCreatedDateFilter;
            set
            {
                SetProperty(ref _selectedCreatedDateFilter, value);
                // 当选择日期时，设置筛选范围为从该日期开始
                CreatedDateFrom = value;
                CreatedDateTo = null; // 不设置结束日期，表示从该日期开始的所有记录
            }
        }

        #endregion

        #region Commands

        public ICommand LoadDataCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ResetFiltersCommand { get; }
        public ICommand ViewOrderDetailsCommand { get; }
        public ICommand RefreshCommand { get; }

        #endregion

        public OrderManagementViewModel()
        {
            System.Diagnostics.Debug.WriteLine("OrderManagementViewModel: Constructor called");

            _apiService = new ApiService();
            _orders = new ObservableCollection<Order>();
            _customers = new ObservableCollection<Customer>();
            _employees = new ObservableCollection<Employee>();
            _products = new ObservableCollection<Product>();
            _statusOptions = new ObservableCollection<string>();
            _salesPersonOptions = new ObservableCollection<Employee>();
            _productOptions = new ObservableCollection<string>();

            // Initialize commands
            LoadDataCommand = new AsyncRelayCommand(async p => await LoadDataAsync());
            SearchCommand = new AsyncRelayCommand(async p => await SearchOrdersAsync());
            ResetFiltersCommand = new RelayCommand(p => ResetFilters());
            ViewOrderDetailsCommand = new RelayCommand(p => ViewOrderDetails(), p => SelectedOrder != null);

            // Initialize filter data
            InitializeFilterData();

            // 订阅客户更新事件
            EventBus.Instance.Subscribe<CustomerUpdatedEvent>(OnCustomerUpdated);
            EventBus.Instance.Subscribe<CustomerAssignedEvent>(OnCustomerAssigned);

            // 订阅员工和产品更新事件
            EventBus.Instance.Subscribe<EmployeeUpdatedEvent>(OnEmployeeUpdated);
            EventBus.Instance.Subscribe<ProductUpdatedEvent>(OnProductUpdated);
        }

        public override async Task LoadDataAsync()
        {
            System.Diagnostics.Debug.WriteLine("OrderManagementViewModel: LoadDataAsync called");

            if (IsDataLoaded)
            {
                System.Diagnostics.Debug.WriteLine("OrderManagementViewModel: Data already loaded, skipping");
                return; // Avoid loading data multiple times
            }

            await LoadOrdersAsync();
            await LoadReferenceDataAsync();
            IsDataLoaded = true;

            System.Diagnostics.Debug.WriteLine("OrderManagementViewModel: LoadDataAsync completed");
        }

        private void InitializeFilterData()
        {
            // Initialize status options
            StatusOptions.Clear();
            StatusOptions.Add("全部");
            StatusOptions.Add("待收款");
            StatusOptions.Add("已收款");
            StatusOptions.Add("已完成");
            StatusOptions.Add("已取消");

            // Set default values
            SelectedStatus = "全部";
            SelectedProduct = "全部";
            SelectedEffectiveDateFilter = null;
            SelectedExpiryDateFilter = null;
            SelectedCreatedDateFilter = null;
        }



        private void UpdateFilterOptionsFromData()
        {
            // Update product options from orders
            var productNames = Orders
                .Where(o => !string.IsNullOrWhiteSpace(o.ProductName))
                .Select(o => o.ProductName)
                .Distinct()
                .OrderBy(p => p)
                .ToList();

            ProductOptions.Clear();
            ProductOptions.Add("全部");
            foreach (var product in productNames)
            {
                ProductOptions.Add(product);
            }

            // Update sales person options from orders - only include employees with role "销售"
            var salesPersons = Orders
                .Where(o => o.SalesPersonId > 0 && !string.IsNullOrWhiteSpace(o.SalesPersonName))
                .GroupBy(o => new { o.SalesPersonId, o.SalesPersonName })
                .Select(g => new Employee { Id = g.Key.SalesPersonId, Name = g.Key.SalesPersonName })
                .Where(e => {
                    // Check if this employee has role "销售"
                    var employee = Employees.FirstOrDefault(emp => emp.Id == e.Id);
                    return employee?.RoleName == "销售";
                })
                .OrderBy(e => e.Name)
                .ToList();

            SalesPersonOptions.Clear();
            SalesPersonOptions.Add(new Employee { Id = 0, Name = "全部" });
            foreach (var salesPerson in salesPersons)
            {
                SalesPersonOptions.Add(salesPerson);
            }
        }

        private async Task LoadOrdersAsync()
        {
            // 使用基类的分页加载功能
            await base.LoadDataAsync();

            // Update filter options based on loaded data
            UpdateFilterOptionsFromData();

            // Update summary
            await UpdateOrderSummaryAsync();
        }

        private async Task LoadReferenceDataAsync()
        {
            try
            {
                // Load customers, employees, and products for dropdowns
                var customersTask = _apiService.GetCustomersAsync();
                var employeesTask = _apiService.GetEmployeesAsync();
                var productsTask = _apiService.GetProductsAsync();

                await Task.WhenAll(customersTask, employeesTask, productsTask);

                // Update collections
                Customers.Clear();
                foreach (var customer in customersTask.Result)
                {
                    Customers.Add(customer);
                }

                Employees.Clear();
                SalesPersonOptions.Clear();
                SalesPersonOptions.Add(new Employee { Id = 0, Name = "全部" }); // Add "All" option
                // Filter to only show employees with role "销售"
                var salesEmployees = employeesTask.Result.Where(e => e.RoleName == "销售").OrderBy(e => e.Name);
                foreach (var employee in employeesTask.Result)
                {
                    Employees.Add(employee);
                }
                foreach (var employee in salesEmployees)
                {
                    SalesPersonOptions.Add(employee);
                }

                Products.Clear();
                if (productsTask.Result != null)
                {
                    foreach (var product in productsTask.Result)
                    {
                        Products.Add(product);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleApiError(ex, "loading reference data");
            }
        }

        private async Task SearchOrdersAsync()
        {
            // 使用新的分页搜索功能
            await SearchAsync(string.Empty); // 订单搜索主要依赖筛选条件，不是文本搜索

            // Update summary for filtered results
            await UpdateOrderSummaryAsync();
        }

        private async Task UpdateOrderSummaryAsync()
        {
            try
            {
                var orderIds = Orders.Select(o => o.Id).ToList();
                if (orderIds.Count > 0)
                {
                    OrderSummary = await _apiService.GetOrderSummaryAsync(orderIds);
                }
                else
                {
                    OrderSummary = new OrderSummary();
                }
            }
            catch (Exception ex)
            {
                // Don't show error for summary calculation failure
                System.Diagnostics.Debug.WriteLine($"Error calculating order summary: {ex.Message}");
                OrderSummary = new OrderSummary();
            }
        }

        private void ResetFilters()
        {
            SearchCustomerName = string.Empty;
            SearchProductName = string.Empty;
            EffectiveDateFrom = null;
            EffectiveDateTo = null;
            ExpiryDateFrom = null;
            ExpiryDateTo = null;
            CreatedDateFrom = null;
            CreatedDateTo = null;
            SelectedStatus = "全部";
            SelectedSalesPersonId = 0;
            SelectedProduct = "全部";
            SelectedEffectiveDateFilter = null;
            SelectedExpiryDateFilter = null;
            SelectedCreatedDateFilter = null;
        }

        private void AddOrder()
        {
            try
            {
                // TODO: Implement add order dialog
                MessageBox.Show("添加订单功能将在后续版本中实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开添加订单对话框失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditOrder()
        {
            if (SelectedOrder == null) return;
            EditOrderRow(SelectedOrder);
        }

        private void EditOrderRow(Order? order)
        {
            if (order == null) return;

            try
            {
                // TODO: Implement edit order dialog
                MessageBox.Show($"编辑订单功能将在后续版本中实现\n订单号: {order.OrderNumber}", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开编辑订单对话框失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task DeleteOrderAsync()
        {
            if (SelectedOrder == null) return;
            await DeleteOrderRowAsync(SelectedOrder);
        }

        private async Task DeleteOrderRowAsync(Order? order)
        {
            if (order == null) return;

            try
            {
                var result = MessageBox.Show(
                    $"确定要删除订单 '{order.OrderNumber}' 吗？\n此操作不可撤销。",
                    "确认删除",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    IsLoading = true;
                    await _apiService.DeleteOrderAsync(order.Id);
                    await LoadOrdersAsync();
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleApiError(ex, "deleting order");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ViewOrderDetails()
        {
            if (SelectedOrder == null) return;
            ViewOrderDetailsRow(SelectedOrder);
        }

        private void ViewOrderDetailsRow(Order? order)
        {
            if (order == null) return;

            try
            {
                // TODO: Implement order details dialog
                MessageBox.Show($"订单详情功能将在后续版本中实现\n订单号: {order.OrderNumber}", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开订单详情对话框失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private void UpdateOrderSummarySync()
        {
            if (OrderSummary == null)
                OrderSummary = new OrderSummary();

            OrderSummary.TotalOrders = Orders.Count;
            OrderSummary.TotalAmount = Orders.Sum(o => o.TotalAmount);
        }

        /// <summary>
        /// 处理客户更新事件
        /// </summary>
        private async void OnCustomerUpdated(CustomerUpdatedEvent eventData)
        {
            try
            {
                // 如果数据已加载，则刷新数据以显示最新的客户信息
                if (IsDataLoaded)
                {
                    System.Diagnostics.Debug.WriteLine($"OrderManagement: 收到客户更新事件 - {eventData.UpdateType}: {eventData.CustomerName}");

                    // 重置数据加载标志，强制重新加载
                    IsDataLoaded = false;
                    await LoadOrdersAsync();
                    await LoadReferenceDataAsync();
                    IsDataLoaded = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理客户更新事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理客户分配事件
        /// </summary>
        private async void OnCustomerAssigned(CustomerAssignedEvent eventData)
        {
            try
            {
                // 如果数据已加载，则刷新数据以显示最新的分配信息
                if (IsDataLoaded)
                {
                    System.Diagnostics.Debug.WriteLine($"OrderManagement: 收到客户分配事件 - {eventData.AssignmentType}: {eventData.CustomerName}");

                    // 重置数据加载标志，强制重新加载
                    IsDataLoaded = false;
                    await LoadOrdersAsync();
                    await LoadReferenceDataAsync();
                    IsDataLoaded = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理客户分配事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理员工更新事件
        /// </summary>
        private async void OnEmployeeUpdated(EmployeeUpdatedEvent eventData)
        {
            try
            {
                // 如果数据已加载，则刷新数据以显示最新的员工信息
                if (IsDataLoaded)
                {
                    System.Diagnostics.Debug.WriteLine($"OrderManagement: 收到员工更新事件 - {eventData.UpdateType}: {eventData.EmployeeName}");

                    // 重置数据加载标志，强制重新加载
                    IsDataLoaded = false;
                    await LoadOrdersAsync();
                    await LoadReferenceDataAsync();
                    IsDataLoaded = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理员工更新事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理产品更新事件
        /// </summary>
        private async void OnProductUpdated(ProductUpdatedEvent eventData)
        {
            try
            {
                // 如果数据已加载，则刷新数据以显示最新的产品信息
                if (IsDataLoaded)
                {
                    System.Diagnostics.Debug.WriteLine($"OrderManagement: 收到产品更新事件 - {eventData.UpdateType}: {eventData.ProductName}");

                    // 重置数据加载标志，强制重新加载
                    IsDataLoaded = false;
                    await LoadOrdersAsync();
                    await LoadReferenceDataAsync();
                    IsDataLoaded = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理产品更新事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否应该显示指定订单（基于权限规则）
        /// </summary>
        /// <param name="order">订单信息</param>
        /// <returns>是否应该显示</returns>
        private bool ShouldShowOrder(Order order)
        {
            // 获取销售人员的分组信息
            var salesPersonGroupId = GetEmployeeGroupId(order.SalesPersonId);

            // 检查是否可以访问该订单的数据：自己名下的订单 + 同组的订单
            return CurrentUser.CanAccessOrderOrAfterSalesData(order.SalesPersonId, salesPersonGroupId);
        }

        /// <summary>
        /// 获取员工的分组ID
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <returns>分组ID</returns>
        private int? GetEmployeeGroupId(int? employeeId)
        {
            if (!employeeId.HasValue)
                return null;

            var employee = Employees.FirstOrDefault(e => e.Id == employeeId.Value);
            return employee?.GroupId;
        }

        #region Abstract Methods Implementation
        /// <summary>
        /// 从API加载所有订单数据
        /// </summary>
        protected override async Task<List<Order>> LoadAllDataFromApiAsync()
        {
            var orders = await _apiService.GetOrdersAsync();

            // 应用权限过滤并按创建时间降序排列（最新的在上面）
            var filteredOrders = orders
                .Where(order => ShouldShowOrder(order))
                .OrderByDescending(order => order.CreatedAt)
                .ToList();

            return filteredOrders;
        }

        /// <summary>
        /// 过滤订单数据（包含搜索关键词和筛选条件）
        /// </summary>
        protected override List<Order> FilterData(List<Order> data, string searchKeyword)
        {
            var filteredData = data.AsEnumerable();

            // 客户名称筛选
            if (!string.IsNullOrWhiteSpace(SearchCustomerName))
            {
                filteredData = filteredData.Where(o =>
                    o.CustomerName?.Contains(SearchCustomerName, StringComparison.OrdinalIgnoreCase) == true);
            }

            // 产品名称筛选
            if (!string.IsNullOrWhiteSpace(SearchProductName))
            {
                filteredData = filteredData.Where(o =>
                    o.ProductNames?.Contains(SearchProductName, StringComparison.OrdinalIgnoreCase) == true);
            }

            // 日期筛选
            if (EffectiveDateFrom.HasValue)
            {
                filteredData = filteredData.Where(o => o.EffectiveDate >= EffectiveDateFrom.Value);
            }
            if (EffectiveDateTo.HasValue)
            {
                filteredData = filteredData.Where(o => o.EffectiveDate <= EffectiveDateTo.Value);
            }
            if (ExpiryDateFrom.HasValue)
            {
                filteredData = filteredData.Where(o => o.ExpiryDate >= ExpiryDateFrom.Value);
            }
            if (ExpiryDateTo.HasValue)
            {
                filteredData = filteredData.Where(o => o.ExpiryDate <= ExpiryDateTo.Value);
            }
            if (CreatedDateFrom.HasValue)
            {
                filteredData = filteredData.Where(o => o.CreatedAt >= CreatedDateFrom.Value);
            }
            if (CreatedDateTo.HasValue)
            {
                filteredData = filteredData.Where(o => o.CreatedAt <= CreatedDateTo.Value);
            }

            // 状态筛选
            if (!string.IsNullOrEmpty(SelectedStatus) && SelectedStatus != "全部")
            {
                filteredData = filteredData.Where(o => o.Status == SelectedStatus);
            }

            // 销售人员筛选
            if (SelectedSalesPersonId.HasValue)
            {
                filteredData = filteredData.Where(o => o.SalesPersonId == SelectedSalesPersonId.Value);
            }

            // 搜索关键词筛选
            if (!string.IsNullOrWhiteSpace(searchKeyword))
            {
                filteredData = filteredData.Where(o =>
                    (o.CustomerName?.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) == true) ||
                    (o.ProductNames?.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) == true) ||
                    (o.OrderNumber?.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) == true) ||
                    (o.SalesPersonName?.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) == true)
                );
            }

            return filteredData.ToList();
        }

        /// <summary>
        /// 数据加载完成时更新UI集合
        /// </summary>
        protected override void OnDataLoaded(List<Order> data)
        {
            Orders.Clear();
            foreach (var order in data)
            {
                Orders.Add(order);
            }
        }
        #endregion
    }
}
