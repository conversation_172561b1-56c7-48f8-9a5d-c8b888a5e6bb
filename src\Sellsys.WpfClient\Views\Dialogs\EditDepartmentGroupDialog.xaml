<Window x:Class="Sellsys.WpfClient.Views.Dialogs.EditDepartmentGroupDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="编辑部门分组" Height="280" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0" Text="编辑部门分组信息" 
                   FontSize="16" FontWeight="Bold" 
                   Foreground="#333333" 
                   Margin="0,0,0,20"/>

        <!-- Form Content -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Department Selection -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="所属部门:" 
                       VerticalAlignment="Center" 
                       Margin="0,0,10,15" 
                       FontWeight="Medium"/>
            <ComboBox x:Name="DepartmentComboBox"
                      Grid.Row="0" Grid.Column="1" 
                      ItemsSource="{Binding Departments}"
                      SelectedItem="{Binding SelectedDepartment}"
                      DisplayMemberPath="Name"
                      Height="30" 
                      Padding="8,5"
                      BorderBrush="#CCCCCC"
                      BorderThickness="1"
                      Margin="0,0,0,15"/>

            <!-- Group Name -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="分组名称:" 
                       VerticalAlignment="Center" 
                       Margin="0,0,10,15" 
                       FontWeight="Medium"/>
            <TextBox x:Name="GroupNameTextBox"
                     Grid.Row="1" Grid.Column="1" 
                     Text="{Binding GroupName, UpdateSourceTrigger=PropertyChanged}"
                     Height="30" 
                     Padding="8,5"
                     BorderBrush="#CCCCCC"
                     BorderThickness="1"
                     Margin="0,0,0,15"/>

            <!-- Loading Indicator -->
            <StackPanel Grid.Row="2" Grid.ColumnSpan="2" 
                        Orientation="Horizontal" 
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Margin="0,10,0,0">
                <ProgressBar IsIndeterminate="True" Width="20" Height="20" Margin="0,0,10,0"/>
                <TextBlock Text="正在加载部门数据..." VerticalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,20,0,0">
            <Button Content="保存" 
                    Command="{Binding SaveCommand}"
                    Style="{StaticResource PrimaryButtonStyle}"
                    Width="80" Height="35" 
                    Margin="0,0,10,0"/>
            <Button Content="取消" 
                    Command="{Binding CancelCommand}"
                    Style="{StaticResource SecondaryButtonStyle}"
                    Width="80" Height="35"/>
        </StackPanel>

        <!-- Saving Overlay -->
        <Border Grid.RowSpan="3" 
                Background="#80000000" 
                Visibility="{Binding IsSaving, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="正在保存..." Foreground="White" HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
