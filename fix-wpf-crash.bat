@echo off
chcp 65001 >nul
echo ========================================
echo WPF应用程序崩溃修复脚本
echo ========================================
echo.

echo 步骤1: 停止所有相关进程...
taskkill /f /im Sellsys.WpfClient.exe 2>nul
taskkill /f /im dotnet.exe 2>nul
timeout /t 2 >nul

echo 步骤2: 清理临时文件...
if exist "src\Sellsys.WpfClient\bin" rmdir /s /q "src\Sellsys.WpfClient\bin"
if exist "src\Sellsys.WpfClient\obj" rmdir /s /q "src\Sellsys.WpfClient\obj"

echo 步骤3: 创建最小化测试版本...
echo 正在备份原始文件...
copy "src\Sellsys.WpfClient\MainWindow.xaml" "src\Sellsys.WpfClient\MainWindow.xaml.backup" >nul
copy "src\Sellsys.WpfClient\ViewModels\MainViewModel.cs" "src\Sellsys.WpfClient\ViewModels\MainViewModel.cs.backup" >nul

echo 步骤4: 创建简化的MainWindow.xaml...
(
echo ^<Window x:Class="Sellsys.WpfClient.MainWindow"
echo         xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
echo         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
echo         Title="测试窗口" Height="600" Width="800"
echo         WindowStartupLocation="CenterScreen"^>
echo     ^<Grid^>
echo         ^<TextBlock Text="测试窗口 - 如果看到这个说明基础框架正常" 
echo                   HorizontalAlignment="Center" 
echo                   VerticalAlignment="Center"
echo                   FontSize="16"/^>
echo     ^</Grid^>
echo ^</Window^>
) > "src\Sellsys.WpfClient\MainWindow_test.xaml"

echo 步骤5: 创建简化的MainWindow.xaml.cs...
(
echo using System.Windows;
echo.
echo namespace Sellsys.WpfClient
echo {
echo     public partial class MainWindow : Window
echo     {
echo         public MainWindow^(^)
echo         {
echo             try
echo             {
echo                 System.Diagnostics.Debug.WriteLine^("MainWindow: 开始初始化..."^);
echo                 InitializeComponent^(^);
echo                 System.Diagnostics.Debug.WriteLine^("MainWindow: 初始化完成"^);
echo             }
echo             catch ^(System.Exception ex^)
echo             {
echo                 System.Diagnostics.Debug.WriteLine^($"MainWindow错误: {ex.Message}"^);
echo                 System.Windows.MessageBox.Show^($"窗口初始化错误: {ex.Message}", "错误"^);
echo                 throw;
echo             }
echo         }
echo     }
echo }
) > "src\Sellsys.WpfClient\MainWindow_test.xaml.cs"

echo 步骤6: 测试基础框架...
copy "src\Sellsys.WpfClient\MainWindow_test.xaml" "src\Sellsys.WpfClient\MainWindow.xaml" >nul
copy "src\Sellsys.WpfClient\MainWindow_test.xaml.cs" "src\Sellsys.WpfClient\MainWindow.xaml.cs" >nul

echo 正在编译测试版本...
dotnet build src/Sellsys.WpfClient --verbosity quiet
if %errorlevel% neq 0 (
    echo 编译失败！
    goto :restore_files
)

echo 正在运行测试版本...
timeout /t 2 >nul
start /wait dotnet run --project src/Sellsys.WpfClient
if %errorlevel% equ 0 (
    echo ✅ 基础框架测试通过！
    echo 问题出现在复杂的UI绑定或ViewModel中
) else (
    echo ❌ 基础框架测试失败！
    echo 问题出现在更底层的配置中
)

:restore_files
echo.
echo 步骤7: 恢复原始文件...
copy "src\Sellsys.WpfClient\MainWindow.xaml.backup" "src\Sellsys.WpfClient\MainWindow.xaml" >nul
copy "src\Sellsys.WpfClient\ViewModels\MainViewModel.cs.backup" "src\Sellsys.WpfClient\ViewModels\MainViewModel.cs" >nul

echo 步骤8: 清理测试文件...
del "src\Sellsys.WpfClient\MainWindow_test.*" 2>nul
del "src\Sellsys.WpfClient\*.backup" 2>nul

echo.
echo ========================================
echo 修复脚本执行完成
echo ========================================
pause
