using Sellsys.WpfClient.Services;
using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace Sellsys.WpfClient.Converters
{
    /// <summary>
    /// 权限到可见性转换器
    /// </summary>
    public class PermissionToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (parameter is not string moduleName)
                return Visibility.Collapsed;

            // 检查当前用户是否有权限访问指定模块
            bool hasPermission = CurrentUser.HasPermission(moduleName);
            
            return hasPermission ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 权限到布尔值转换器
    /// </summary>
    public class PermissionToBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (parameter is not string moduleName)
                return false;

            // 检查当前用户是否有权限访问指定模块
            return CurrentUser.HasPermission(moduleName);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 分配权限到可见性转换器
    /// </summary>
    public class AssignPermissionToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (parameter is not string assignType)
                return Visibility.Collapsed;

            bool hasPermission = assignType.ToLower() switch
            {
                "sales" => CurrentUser.CanAssignSales(),
                "support" => CurrentUser.CanAssignSupport(),
                _ => CurrentUser.CanAssignData()
            };

            return hasPermission ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }


}
