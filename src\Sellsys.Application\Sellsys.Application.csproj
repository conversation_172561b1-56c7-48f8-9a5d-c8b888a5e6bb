﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\Sellsys.Domain\Sellsys.Domain.csproj" />
    <ProjectReference Include="..\Sellsys.CrossCutting\Sellsys.CrossCutting.csproj" />
    <ProjectReference Include="..\Sellsys.Infrastructure\Sellsys.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.6" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
