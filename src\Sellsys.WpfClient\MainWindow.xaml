﻿<Window x:Class="Sellsys.WpfClient.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Sellsys.WpfClient"
        xmlns:views="clr-namespace:Sellsys.WpfClient.Views"
        xmlns:viewmodels="clr-namespace:Sellsys.WpfClient.ViewModels"
        mc:Ignorable="d"
        Title="巨炜科技客户管理信息系统"
        Icon="Resources/favicon.ico"
        MinHeight="600" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        ShowInTaskbar="True"
        Visibility="Visible"
        Topmost="False">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/> <!-- Top Bar -->
            <RowDefinition Height="*"/>   <!-- Content -->
        </Grid.RowDefinitions>

        <!-- Top Bar -->
        <Grid Grid.Row="0" Background="{StaticResource TopBarBackgroundBrush}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 系统标题 -->
            <TextBlock Grid.Column="0"
                       Text="巨炜科技客户管理信息系统"
                       Style="{StaticResource TitleTextStyle}"
                       VerticalAlignment="Center"
                       Margin="20,0,0,0"/>

            <!-- 用户信息和登出 -->
            <StackPanel Grid.Column="1"
                       Orientation="Horizontal"
                       VerticalAlignment="Center"
                       Margin="0,0,20,0">

                <!-- 用户信息 -->
                <TextBlock Text="{Binding CurrentUserInfo}"
                          Foreground="White"
                          FontSize="14"
                          VerticalAlignment="Center"
                          Margin="0,0,15,0"/>

                <!-- 登出按钮 -->
                <Button Content="登出"
                       Command="{Binding LogoutCommand}"
                       Background="#FF6C757D"
                       Foreground="White"
                       BorderThickness="0"
                       Padding="15,8"
                       FontSize="12"
                       Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               CornerRadius="3"
                                               Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#FF5A6268"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>
        </Grid>

        <!-- Main Content Area -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="140"/> <!-- Left Navigation -->
                <ColumnDefinition Width="*"/>   <!-- Main View -->
            </Grid.ColumnDefinitions>

            <!-- Left Navigation Panel -->
            <Border Grid.Column="0" Background="{StaticResource LeftMenuBackgroundBrush}">
                <ScrollViewer VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled"
                              Margin="0"
                              Padding="0">
                    <StackPanel Margin="5,20,10,20">
                        <!-- Navigation Buttons -->
                        <Button Content="客户管理"
                                Command="{Binding ShowCustomerManagementViewCommand}"
                                Style="{Binding CurrentViewName, Converter={StaticResource NavigationButtonStyleConverter}, ConverterParameter=CustomerManagement}"/>

                        <Button Content="销售管理"
                                Command="{Binding ShowSalesManagementViewCommand}"
                                Style="{Binding CurrentViewName, Converter={StaticResource NavigationButtonStyleConverter}, ConverterParameter=SalesManagement}"/>

                        <Button Content="订单管理"
                                Command="{Binding ShowOrderManagementViewCommand}"
                                Style="{Binding CurrentViewName, Converter={StaticResource NavigationButtonStyleConverter}, ConverterParameter=OrderManagement}"/>

                        <Button Content="售后服务"
                                Command="{Binding ShowAfterSalesViewCommand}"
                                Style="{Binding CurrentViewName, Converter={StaticResource NavigationButtonStyleConverter}, ConverterParameter=AfterSales}"/>

                        <Button Content="产品管理"
                                Command="{Binding ShowProductManagementViewCommand}"
                                Style="{Binding CurrentViewName, Converter={StaticResource NavigationButtonStyleConverter}, ConverterParameter=ProductManagement}"/>

                        <Button Content="财务管理"
                                Command="{Binding ShowFinanceManagementViewCommand}"
                                Style="{Binding CurrentViewName, Converter={StaticResource NavigationButtonStyleConverter}, ConverterParameter=FinanceManagement}"/>

                        <Button Content="系统设置"
                                Command="{Binding ShowSystemSettingsViewCommand}"
                                Style="{Binding CurrentViewName, Converter={StaticResource NavigationButtonStyleConverter}, ConverterParameter=SystemSettings}"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- Content Display -->
            <ContentControl Grid.Column="1"
                            Content="{Binding CurrentView}"
                            Margin="0"
                            Background="White"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"/>
        </Grid>

        <!-- Dialog Layer -->
        <Grid Visibility="{Binding DialogViewModel, Converter={StaticResource NotNullToVisibleConverter}}">
            <Border Background="#7F000000"/>
            <ContentControl Content="{Binding DialogViewModel}"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"/>
        </Grid>
    </Grid>
</Window>
