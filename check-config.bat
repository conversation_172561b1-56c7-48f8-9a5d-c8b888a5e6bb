@echo off
echo ========================================
echo           System Configuration Check
echo ========================================
echo.

cd /d "%~dp0"

echo [%time%] Starting system configuration check...
echo.

REM Check .NET SDK
echo 1. Checking .NET SDK...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo [X] .NET SDK not installed
    echo     Please download from https://dotnet.microsoft.com/download/dotnet/8.0
) else (
    echo [OK] .NET SDK installed
    echo     Version:
    dotnet --version
)
echo.

REM Check project files
echo 2. Checking project files...
if exist "src\Sellsys.WebApi\Sellsys.WebApi.csproj" (
    echo [OK] Backend project file exists
) else (
    echo [X] Backend project file missing: src\Sellsys.WebApi\Sellsys.WebApi.csproj
)

if exist "src\Sellsys.WpfClient\Sellsys.WpfClient.csproj" (
    echo [OK] Frontend project file exists
) else (
    echo [X] Frontend project file missing: src\Sellsys.WpfClient\Sellsys.WpfClient.csproj
)
echo.

REM 检查配置文件
echo 3. 检查配置文件...
if exist "src\Sellsys.WebApi\appsettings.json" (
    echo [✅] 后端配置文件存在
) else (
    echo [❌] 后端配置文件不存在: src\Sellsys.WebApi\appsettings.json
)

if exist "src\Sellsys.WpfClient\appsettings.json" (
    echo [✅] 前端配置文件存在
) else (
    echo [❌] 前端配置文件不存在: src\Sellsys.WpfClient\appsettings.json
)
echo.

REM 检查数据库文件
echo 4. 检查数据库文件...
if exist "src\Sellsys.WebApi\sellsys.db" (
    echo [✅] 数据库文件存在: src\Sellsys.WebApi\sellsys.db
    for %%A in ("src\Sellsys.WebApi\sellsys.db") do echo     大小: %%~zA 字节
) else (
    echo [ℹ️] 数据库文件不存在，首次运行时会自动创建
)

if exist "src\Sellsys.Infrastructure\sellsys.db" (
    echo [✅] 基础设施数据库文件存在: src\Sellsys.Infrastructure\sellsys.db
) else (
    echo [ℹ️] 基础设施数据库文件不存在
)
echo.

REM 检查资源文件
echo 5. 检查前端资源文件...
if exist "src\Sellsys.WpfClient\Resources\Styles\GlobalStyles.xaml" (
    echo [✅] 全局样式文件存在
) else (
    echo [❌] 全局样式文件不存在: src\Sellsys.WpfClient\Resources\Styles\GlobalStyles.xaml
)

if exist "src\Sellsys.WpfClient\Resources\favicon.ico" (
    echo [✅] 图标文件存在
) else (
    echo [❌] 图标文件不存在: src\Sellsys.WpfClient\Resources\favicon.ico
)
echo.

REM 检查端口占用
echo 6. 检查端口占用情况...
netstat -an | findstr ":5078" >nul
if errorlevel 1 (
    echo [✅] 端口 5078 未被占用
) else (
    echo [⚠️] 端口 5078 已被占用
    echo     当前占用情况:
    netstat -ano | findstr ":5078"
)
echo.

REM 尝试构建项目
echo 7. 尝试构建项目...
echo    构建后端项目...
dotnet build src\Sellsys.WebApi --verbosity quiet >nul 2>&1
if errorlevel 1 (
    echo [❌] 后端项目构建失败
    echo     请运行以下命令查看详细错误:
    echo     dotnet build src\Sellsys.WebApi
) else (
    echo [✅] 后端项目构建成功
)

echo    构建前端项目...
dotnet build src\Sellsys.WpfClient --verbosity quiet >nul 2>&1
if errorlevel 1 (
    echo [❌] 前端项目构建失败
    echo     请运行以下命令查看详细错误:
    echo     dotnet build src\Sellsys.WpfClient
) else (
    echo [✅] 前端项目构建成功
)
echo.

REM 检查启动脚本
echo 8. 检查启动脚本...
if exist "start-backend.bat" (
    echo [✅] 后端启动脚本存在
) else (
    echo [❌] 后端启动脚本不存在: start-backend.bat
)

if exist "start-frontend.bat" (
    echo [✅] 前端启动脚本存在
) else (
    echo [❌] 前端启动脚本不存在: start-frontend.bat
)

if exist "start-system.bat" (
    echo [✅] 系统启动脚本存在
) else (
    echo [❌] 系统启动脚本不存在: start-system.bat
)
echo.

echo ========================================
echo           配置检查完成
echo ========================================
echo.
echo 如果所有检查都通过，可以运行 start-system.bat 启动系统
echo.
pause
