<Window x:Class="Sellsys.WpfClient.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="销售管理系统 - 登录"
        Icon="../Resources/favicon.ico"
        Height="726" Width="1280"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">

    <Window.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- 登录按钮样式 -->
        <Style x:Key="LoginButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF0078D4"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="3"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF106EBE"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF005A9E"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 输入框样式 -->
        <Style x:Key="LoginTextBoxStyle" TargetType="TextBox">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="2">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#FF0078D4"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 密码框样式 -->
        <Style x:Key="LoginPasswordBoxStyle" TargetType="PasswordBox">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="PasswordBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="2">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#FF0078D4"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <!-- 背景图片 -->
        <Grid.Background>
            <ImageBrush ImageSource="/Resources/login.jpg" Stretch="Fill"/>
        </Grid.Background>

        <!-- 关闭按钮 -->
        <Button x:Name="CloseButton"
                HorizontalAlignment="Right"
                VerticalAlignment="Top"
                Width="30" Height="30"
                Margin="10"
                Background="#80000000"
                BorderThickness="0"
                Foreground="White"
                FontSize="16"
                FontWeight="Bold"
                Content="×"
                Cursor="Hand"
                Click="CloseButton_Click"/>

        <!-- 登录表单 - 直接在背景上，无边框 -->
        <Grid HorizontalAlignment="Right"
              VerticalAlignment="Bottom"
              Width="450"
              Height="200"
              Margin="0,0,150,120">

            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="15"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="15"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="20"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="15"/>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="15"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 登录账号标签 -->
            <TextBlock Grid.Row="0" Grid.Column="0"
                      Text="登录账号："
                      FontSize="16"
                      FontWeight="Medium"
                      Foreground="#FF2C3E50"
                      VerticalAlignment="Center"/>

            <!-- 用户名输入框 -->
            <TextBox Grid.Row="0" Grid.Column="2"
                    x:Name="UsernameTextBox"
                    Style="{StaticResource LoginTextBoxStyle}"
                    Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                    Height="40"
                    TabIndex="1"/>

            <!-- 登录按钮 -->
            <Button Grid.Row="0" Grid.Column="4" Grid.RowSpan="3"
                   x:Name="LoginButton"
                   Content="登 录"
                   Width="80"
                   Height="85"
                   Style="{StaticResource LoginButtonStyle}"
                   Command="{Binding LoginCommand}"
                   TabIndex="3"/>

            <!-- 输入密码标签 -->
            <TextBlock Grid.Row="2" Grid.Column="0"
                      Text="输入密码："
                      FontSize="16"
                      FontWeight="Medium"
                      Foreground="#FF2C3E50"
                      VerticalAlignment="Center"/>

            <!-- 密码输入框 -->
            <PasswordBox Grid.Row="2" Grid.Column="2"
                        x:Name="PasswordBox"
                        Style="{StaticResource LoginPasswordBoxStyle}"
                        Height="40"
                        TabIndex="2"
                        KeyDown="PasswordBox_KeyDown"/>

            <!-- 记住账号复选框 -->
            <CheckBox Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="3"
                     x:Name="RememberCheckBox"
                     Content="记住账号"
                     FontSize="14"
                     Foreground="#FF2C3E50"
                     VerticalAlignment="Center"
                     IsChecked="{Binding RememberUsername}"/>
        </Grid>

        <!-- 加载指示器 -->
        <Border x:Name="LoadingOverlay"
                Background="#80000000"
                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch">
            <StackPanel HorizontalAlignment="Center"
                       VerticalAlignment="Center">
                <ProgressBar Width="100"
                           Height="20"
                           IsIndeterminate="True"
                           Foreground="#FF4A90E2"/>
                <TextBlock Text="正在登录..."
                          Foreground="White"
                          FontSize="14"
                          HorizontalAlignment="Center"
                          Margin="0,10,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
